"""
Centralized Error Handling Middleware for EduFair
Provides consistent error responses and comprehensive error logging
"""

import logging
import traceback
import uuid
from datetime import datetime, timezone
from typing import Union

from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from fastapi.exceptions import RequestValidationError
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

# Get loggers
logger = logging.getLogger(__name__)
security_logger = logging.getLogger('security')
error_logger = logging.getLogger('errors')


class ErrorResponse:
    """Standardized error response format"""
    
    def __init__(
        self,
        error: bool = True,
        message: str = "An error occurred",
        status_code: int = 500,
        error_code: str = None,
        details: dict = None,
        request_id: str = None
    ):
        self.error = error
        self.message = message
        self.status_code = status_code
        self.error_code = error_code or f"ERR_{status_code}"
        self.details = details or {}
        self.request_id = request_id or str(uuid.uuid4())
        self.timestamp = datetime.now(timezone.utc).isoformat()
    
    def to_dict(self) -> dict:
        return {
            "error": self.error,
            "message": self.message,
            "status_code": self.status_code,
            "error_code": self.error_code,
            "details": self.details,
            "request_id": self.request_id,
            "timestamp": self.timestamp
        }


async def global_exception_handler(request: Request, exc: Exception) -> JSONResponse:
    """
    Global exception handler for all unhandled exceptions
    """
    request_id = str(uuid.uuid4())
    
    # Extract request information
    request_info = {
        "method": request.method,
        "url": str(request.url),
        "path": request.url.path,
        "query_params": dict(request.query_params),
        "client_ip": request.client.host if request.client else "unknown",
        "user_agent": request.headers.get("user-agent", "unknown"),
        "request_id": request_id
    }
    
    # Handle different exception types
    if isinstance(exc, HTTPException):
        return await handle_http_exception(request, exc, request_info)
    elif isinstance(exc, RequestValidationError):
        return await handle_validation_error(request, exc, request_info)
    elif isinstance(exc, ValidationError):
        return await handle_pydantic_validation_error(request, exc, request_info)
    elif isinstance(exc, SQLAlchemyError):
        return await handle_database_error(request, exc, request_info)
    else:
        return await handle_unexpected_error(request, exc, request_info)


async def handle_http_exception(request: Request, exc: HTTPException, request_info: dict) -> JSONResponse:
    """Handle FastAPI HTTP exceptions"""
    
    # Log security-related errors
    if exc.status_code in [401, 403, 429]:
        security_logger.warning(
            f"Security event: {exc.status_code} - {exc.detail}",
            extra={
                **request_info,
                "security_event": True,
                "status_code": exc.status_code
            }
        )
    
    # Log client errors (4xx) as warnings, server errors (5xx) as errors
    if 400 <= exc.status_code < 500:
        logger.warning(f"Client error {exc.status_code}: {exc.detail}", extra=request_info)
    else:
        logger.error(f"Server error {exc.status_code}: {exc.detail}", extra=request_info)
    
    error_response = ErrorResponse(
        message=exc.detail,
        status_code=exc.status_code,
        error_code=f"HTTP_{exc.status_code}",
        request_id=request_info["request_id"]
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.to_dict()
    )


async def handle_validation_error(request: Request, exc: RequestValidationError, request_info: dict) -> JSONResponse:
    """Handle FastAPI request validation errors"""
    
    validation_details = []
    for error in exc.errors():
        validation_details.append({
            "field": " -> ".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    logger.warning(
        f"Validation error: {len(validation_details)} field(s) failed validation",
        extra={**request_info, "validation_errors": validation_details}
    )
    
    error_response = ErrorResponse(
        message="Request validation failed",
        status_code=422,
        error_code="VALIDATION_ERROR",
        details={"validation_errors": validation_details},
        request_id=request_info["request_id"]
    )
    
    return JSONResponse(
        status_code=422,
        content=error_response.to_dict()
    )


async def handle_pydantic_validation_error(request: Request, exc: ValidationError, request_info: dict) -> JSONResponse:
    """Handle Pydantic validation errors"""
    
    validation_details = []
    for error in exc.errors():
        validation_details.append({
            "field": " -> ".join(str(loc) for loc in error["loc"]),
            "message": error["msg"],
            "type": error["type"]
        })
    
    logger.warning(
        f"Pydantic validation error: {len(validation_details)} field(s) failed validation",
        extra={**request_info, "validation_errors": validation_details}
    )
    
    error_response = ErrorResponse(
        message="Data validation failed",
        status_code=422,
        error_code="PYDANTIC_VALIDATION_ERROR",
        details={"validation_errors": validation_details},
        request_id=request_info["request_id"]
    )
    
    return JSONResponse(
        status_code=422,
        content=error_response.to_dict()
    )


async def handle_database_error(request: Request, exc: SQLAlchemyError, request_info: dict) -> JSONResponse:
    """Handle database-related errors"""
    
    error_logger.error(
        f"Database error: {type(exc).__name__} - {str(exc)}",
        extra={
            **request_info,
            "exception_type": type(exc).__name__,
            "traceback": traceback.format_exc()
        }
    )
    
    # Don't expose internal database errors to clients
    error_response = ErrorResponse(
        message="A database error occurred. Please try again later.",
        status_code=500,
        error_code="DATABASE_ERROR",
        request_id=request_info["request_id"]
    )
    
    return JSONResponse(
        status_code=500,
        content=error_response.to_dict()
    )


async def handle_unexpected_error(request: Request, exc: Exception, request_info: dict) -> JSONResponse:
    """Handle unexpected/unhandled errors"""
    
    error_logger.error(
        f"Unexpected error: {type(exc).__name__} - {str(exc)}",
        extra={
            **request_info,
            "exception_type": type(exc).__name__,
            "traceback": traceback.format_exc()
        }
    )
    
    # Don't expose internal error details to clients
    error_response = ErrorResponse(
        message="An unexpected error occurred. Please try again later.",
        status_code=500,
        error_code="INTERNAL_SERVER_ERROR",
        request_id=request_info["request_id"]
    )
    
    return JSONResponse(
        status_code=500,
        content=error_response.to_dict()
    )


# Custom exception classes for business logic
class BusinessLogicError(Exception):
    """Base exception for business logic errors"""
    def __init__(self, message: str, error_code: str = None, status_code: int = 400):
        self.message = message
        self.error_code = error_code or "BUSINESS_LOGIC_ERROR"
        self.status_code = status_code
        super().__init__(self.message)


class AuthenticationError(BusinessLogicError):
    """Authentication-related errors"""
    def __init__(self, message: str = "Authentication failed"):
        super().__init__(message, "AUTHENTICATION_ERROR", 401)


class AuthorizationError(BusinessLogicError):
    """Authorization-related errors"""
    def __init__(self, message: str = "Access denied"):
        super().__init__(message, "AUTHORIZATION_ERROR", 403)


class ResourceNotFoundError(BusinessLogicError):
    """Resource not found errors"""
    def __init__(self, message: str = "Resource not found"):
        super().__init__(message, "RESOURCE_NOT_FOUND", 404)


class ConflictError(BusinessLogicError):
    """Resource conflict errors"""
    def __init__(self, message: str = "Resource conflict"):
        super().__init__(message, "CONFLICT_ERROR", 409)


class RateLimitError(BusinessLogicError):
    """Rate limiting errors"""
    def __init__(self, message: str = "Rate limit exceeded"):
        super().__init__(message, "RATE_LIMIT_ERROR", 429)


async def handle_business_logic_error(request: Request, exc: BusinessLogicError) -> JSONResponse:
    """Handle custom business logic errors"""
    
    request_id = str(uuid.uuid4())
    request_info = {
        "method": request.method,
        "url": str(request.url),
        "path": request.url.path,
        "client_ip": request.client.host if request.client else "unknown",
        "request_id": request_id
    }
    
    # Log security events for auth errors
    if isinstance(exc, (AuthenticationError, AuthorizationError)):
        security_logger.warning(
            f"Security event: {exc.error_code} - {exc.message}",
            extra={**request_info, "security_event": True}
        )
    else:
        logger.warning(f"Business logic error: {exc.error_code} - {exc.message}", extra=request_info)
    
    error_response = ErrorResponse(
        message=exc.message,
        status_code=exc.status_code,
        error_code=exc.error_code,
        request_id=request_id
    )
    
    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.to_dict()
    )
