from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy.orm import Session
from fastapi import HTTPException
from datetime import datetime, timezone
from decimal import Decimal

# Import Models
from Models.Events import EventPayment, EventRegistration, PaymentStatusEnum, RegistrationStatusEnum
from Models.users import User

# Import Schemas
from Schemas.Events.Events import EventPaymentCreate, EventPaymentOut

# Import services
from services import payment_service


def get_payment_service():
    """Get the payment service (now always dummy)"""
    return payment_service  # This is now the dummy service


def create_payment_for_registration(
    db: Session, 
    registration_id: uuid.UUID, 
    payment_method: str,
    user_id: uuid.UUID
) -> Dict[str, Any]:
    """Create payment for event registration"""
    # Get registration
    registration = db.query(EventRegistration).filter(
        EventRegistration.id == registration_id,
        EventRegistration.user_id == user_id
    ).first()
    
    if not registration:
        raise HTTPException(status_code=404, detail="Registration not found")
    
    if registration.payment_status == PaymentStatusEnum.COMPLETED:
        raise HTTPException(status_code=400, detail="Payment already completed")
    
    if registration.total_amount <= 0:
        raise HTTPException(status_code=400, detail="No payment required for this registration")
    
    # Process payment
    try:
        current_payment_service = get_payment_service()
        payment = current_payment_service.process_event_payment(
            db=db,
            registration_id=registration_id,
            payment_method=payment_method,
            amount=registration.total_amount,
            currency=registration.currency
        )
        
        return {
            "success": True,
            "payment": payment,
            "client_secret": payment.gateway_response.get("client_secret") if payment.gateway_response else None,
            "payment_intent_id": payment.gateway_payment_intent_id
        }
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


def confirm_payment(
    db: Session,
    payment_intent_id: str,
    user_id: uuid.UUID
) -> EventPaymentOut:
    """Confirm payment after successful Stripe payment"""
    # Find payment by payment intent ID
    payment = db.query(EventPayment).filter(
        EventPayment.gateway_payment_intent_id == payment_intent_id
    ).first()
    
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    # Verify user owns this payment
    registration = db.query(EventRegistration).filter(
        EventRegistration.id == payment.registration_id,
        EventRegistration.user_id == user_id
    ).first()
    
    if not registration:
        raise HTTPException(status_code=403, detail="Access denied")
    
    current_payment_service = get_payment_service()
    return current_payment_service.confirm_event_payment(db, payment.id, payment_intent_id)


def get_payment_by_id(db: Session, payment_id: uuid.UUID, user_id: uuid.UUID) -> EventPaymentOut:
    """Get payment by ID"""
    payment = db.query(EventPayment).filter(EventPayment.id == payment_id).first()
    
    if not payment:
        raise HTTPException(status_code=404, detail="Payment not found")
    
    # Verify user owns this payment
    registration = db.query(EventRegistration).filter(
        EventRegistration.id == payment.registration_id,
        EventRegistration.user_id == user_id
    ).first()
    
    if not registration:
        raise HTTPException(status_code=403, detail="Access denied")
    
    return EventPaymentOut.model_validate(payment)


def get_user_payments(
    db: Session, 
    user_id: uuid.UUID, 
    skip: int = 0, 
    limit: int = 20
) -> List[EventPaymentOut]:
    """Get user's payments"""
    payments = db.query(EventPayment).join(EventRegistration).filter(
        EventRegistration.user_id == user_id
    ).offset(skip).limit(limit).all()
    
    return [EventPaymentOut.model_validate(payment) for payment in payments]


def get_registration_payments(
    db: Session, 
    registration_id: uuid.UUID, 
    user_id: uuid.UUID
) -> List[EventPaymentOut]:
    """Get payments for a specific registration"""
    # Verify user owns the registration
    registration = db.query(EventRegistration).filter(
        EventRegistration.id == registration_id,
        EventRegistration.user_id == user_id
    ).first()
    
    if not registration:
        raise HTTPException(status_code=404, detail="Registration not found")
    
    payments = db.query(EventPayment).filter(
        EventPayment.registration_id == registration_id
    ).all()
    
    return [EventPaymentOut.model_validate(payment) for payment in payments]


def request_refund(
    db: Session,
    payment_id: uuid.UUID,
    user_id: uuid.UUID,
    refund_reason: str = "requested_by_customer"
) -> EventPaymentOut:
    """Request refund for a payment"""
    payment = db.query(EventPayment).filter(
        EventPayment.id == payment_id,
        EventPayment.status == PaymentStatusEnum.COMPLETED
    ).first()
    
    if not payment:
        raise HTTPException(status_code=404, detail="Completed payment not found")
    
    # Verify user owns this payment
    registration = db.query(EventRegistration).filter(
        EventRegistration.id == payment.registration_id,
        EventRegistration.user_id == user_id
    ).first()
    
    if not registration:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Check if event allows refunds
    # This would typically check the event's refund policy and timing
    # For now, we'll allow refunds for any completed payment
    
    current_payment_service = get_payment_service()
    return current_payment_service.refund_event_payment(
        db=db,
        payment_id=payment_id,
        refund_reason=refund_reason
    )


def process_webhook_event(db: Session, event_data: Dict[str, Any]) -> Dict[str, Any]:
    """Process payment webhook events (dummy service)"""
    event_type = event_data.get("type")
    
    if event_type == "payment_intent.succeeded":
        payment_intent = event_data["data"]["object"]
        payment_intent_id = payment_intent["id"]
        
        # Find and update payment
        payment = db.query(EventPayment).filter(
            EventPayment.gateway_payment_intent_id == payment_intent_id
        ).first()
        
        if payment:
            payment.status = PaymentStatusEnum.COMPLETED
            payment.processed_at = datetime.now(timezone.utc)
            payment.gateway_transaction_id = payment_intent.get("latest_charge")
            
            # Update registration
            registration = db.query(EventRegistration).filter(
                EventRegistration.id == payment.registration_id
            ).first()
            
            if registration:
                registration.payment_status = PaymentStatusEnum.COMPLETED
                registration.payment_reference = payment.gateway_transaction_id
                registration.confirmed_at = datetime.now(timezone.utc)
                registration.status = RegistrationStatusEnum.CONFIRMED
            
            db.commit()
            
            return {"success": True, "message": "Payment confirmed"}
    
    elif event_type == "payment_intent.payment_failed":
        payment_intent = event_data["data"]["object"]
        payment_intent_id = payment_intent["id"]
        
        # Find and update payment
        payment = db.query(EventPayment).filter(
            EventPayment.gateway_payment_intent_id == payment_intent_id
        ).first()
        
        if payment:
            payment.status = PaymentStatusEnum.FAILED
            payment.failed_at = datetime.now(timezone.utc)
            payment.failure_reason = payment_intent.get("last_payment_error", {}).get("message", "Payment failed")
            
            # Update registration
            registration = db.query(EventRegistration).filter(
                EventRegistration.id == payment.registration_id
            ).first()
            
            if registration:
                registration.payment_status = PaymentStatusEnum.FAILED
            
            db.commit()
            
            return {"success": True, "message": "Payment failure recorded"}
    
    elif event_type == "charge.dispute.created":
        # Handle chargeback/dispute
        charge = event_data["data"]["object"]
        charge_id = charge["id"]
        
        # Find payment by charge ID and handle dispute
        payment = db.query(EventPayment).filter(
            EventPayment.gateway_transaction_id == charge_id
        ).first()
        
        if payment:
            # You might want to notify admins about the dispute
            # For now, we'll just log it
            return {"success": True, "message": "Dispute recorded"}
    
    return {"success": True, "message": "Event processed"}


def get_payment_statistics(db: Session, organizer_id: uuid.UUID) -> Dict[str, Any]:
    """Get payment statistics for event organizer"""
    from sqlalchemy import func
    from Models.Events import Event
    
    # Get total revenue from organizer's events
    revenue_query = db.query(
        func.sum(EventPayment.amount).label("total_revenue"),
        func.count(EventPayment.id).label("total_payments")
    ).join(EventRegistration).join(Event).filter(
        Event.organizer_id == organizer_id,
        EventPayment.status == PaymentStatusEnum.COMPLETED
    ).first()
    
    # Get payment status breakdown
    status_breakdown = db.query(
        EventPayment.status,
        func.count(EventPayment.id).label("count"),
        func.sum(EventPayment.amount).label("amount")
    ).join(EventRegistration).join(Event).filter(
        Event.organizer_id == organizer_id
    ).group_by(EventPayment.status).all()
    
    # Get refund statistics
    refund_stats = db.query(
        func.count(EventPayment.id).label("refund_count"),
        func.sum(EventPayment.refund_amount).label("total_refunded")
    ).join(EventRegistration).join(Event).filter(
        Event.organizer_id == organizer_id,
        EventPayment.status == PaymentStatusEnum.REFUNDED
    ).first()
    
    return {
        "total_revenue": float(revenue_query.total_revenue or 0),
        "total_payments": revenue_query.total_payments or 0,
        "status_breakdown": [
            {
                "status": status.status.value,
                "count": status.count,
                "amount": float(status.amount or 0)
            }
            for status in status_breakdown
        ],
        "refund_count": refund_stats.refund_count or 0,
        "total_refunded": float(refund_stats.total_refunded or 0)
    }
