import uuid
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception
from Models.Class import ClassNumber
from Schemas.TeacherModule.Class import ClassNumberCreate, ClassNumberUpdate

def create_class_number(db: Session, class_in: ClassNumberCreate) -> ClassNumber:
    new_class = ClassNumber(ClassNo=class_in.ClassNo)
    db.add(new_class)
    db.commit()
    db.refresh(new_class)
    return new_class

def get_all_class_numbers(db: Session):
    return db.query(ClassNumber).all()

def get_class_number_by_id(db: Session, class_id: uuid.UUID):
    class_number = db.query(ClassNumber).filter(ClassNumber.id == class_id).first()
    if not class_number:
        raise HTTPException(status_code=404, detail="ClassNumber not found.")
    return class_number

def update_class_number(db: Session, class_id: uuid.UUID, class_update: ClassNumberUpdate):
    class_number = db.query(ClassNumber).filter(ClassNumber.id == class_id).first()
    if not class_number:
        raise HTTPException(status_code=404, detail="ClassNumber not found.")
    if class_update.ClassNo is not None:
        class_number.ClassNo = class_update.ClassNo
    db.commit()
    db.refresh(class_number)
    return class_number

def delete_class_number(db: Session, class_id: uuid.UUID):
    class_number = db.query(ClassNumber).filter(ClassNumber.id == class_id).first()
    if not class_number:
        raise HTTPException(status_code=404, detail="ClassNumber not found.")
    db.delete(class_number)
    db.commit()
    return None 