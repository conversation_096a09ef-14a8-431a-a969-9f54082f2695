from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, EmailStr
from datetime import datetime
from uuid import UUID
from decimal import Decimal


class MentorApplicationOut(BaseModel):
    """Mentor application for institute review"""
    id: UUID
    applicant_id: UUID
    applicant_name: str = Field(..., description="Full name of applicant")
    applicant_email: EmailStr = Field(..., description="Email of applicant")
    status: str = Field(..., description="Application status")
    application_date: datetime = Field(..., description="When application was submitted")
    application_message: Optional[str] = Field(None, description="Application message from mentor")
    expertise: List[str] = Field(default=[], description="Areas of expertise")
    proposed_hourly_rate: Optional[Decimal] = Field(None, description="Proposed hourly rate")
    availability_hours: Optional[int] = Field(None, description="Available hours per week")
    experience_years: Optional[int] = Field(None, description="Years of experience")
    education: Optional[str] = Field(None, description="Educational background")
    current_position: Optional[str] = Field(None, description="Current job position")
    linkedin_url: Optional[str] = Field(None, description="LinkedIn profile")
    resume_url: Optional[str] = Field(None, description="Resume file URL")


class MentorApplicationsResponse(BaseModel):
    """Response for mentor applications list"""
    applications: List[MentorApplicationOut]
    total: int
    pending: int
    approved: int
    rejected: int


class MentorInvitationCreate(BaseModel):
    """Create invitation for mentor to join institute"""
    mentor_id: UUID = Field(..., description="UUID of mentor to invite")
    invitation_message: str = Field(..., description="Personal invitation message")
    proposed_hourly_rate: Optional[Decimal] = Field(None, description="Proposed hourly rate")
    proposed_hours_per_week: Optional[int] = Field(None, description="Proposed hours per week")
    expertise_areas_needed: List[str] = Field(default=[], description="Expertise areas needed")
    start_date: Optional[datetime] = Field(None, description="Proposed start date")
    contract_terms: Optional[str] = Field(None, description="Contract terms")


class MentorInvitationOut(BaseModel):
    """Mentor invitation details"""
    id: UUID
    mentor_id: Optional[UUID] = Field(None, description="Mentor ID if mentor exists")
    mentor_email: EmailStr
    mentor_name: Optional[str] = Field(None, description="Mentor name if exists")
    status: str = Field(..., description="Invitation status")
    invitation_message: str
    proposed_hourly_rate: Optional[Decimal]
    proposed_hours_per_week: Optional[int]
    expertise_areas_needed: List[str]
    invited_at: datetime
    responded_at: Optional[datetime]
    expires_at: Optional[datetime]


class ApplicationApprovalRequest(BaseModel):
    """Request to approve mentor application"""
    hourly_rate: Optional[Decimal] = Field(None, description="Approved hourly rate")
    hours_per_week: Optional[int] = Field(None, description="Approved hours per week")
    start_date: Optional[datetime] = Field(None, description="Start date")
    contract_terms: Optional[str] = Field(None, description="Contract terms")
    approval_message: Optional[str] = Field(None, description="Approval message")


class ApplicationRejectionRequest(BaseModel):
    """Request to reject mentor application"""
    rejection_reason: str = Field(..., description="Reason for rejection")
    rejection_message: Optional[str] = Field(None, description="Message to mentor")


class InstituteMentorOut(BaseModel):
    """Mentor details for institute view"""
    id: UUID
    first_name: str
    last_name: str
    email: EmailStr
    phone: Optional[str]
    status: str = Field(..., description="Association status with institute")
    join_date: Optional[datetime] = Field(None, description="Date joined institute")
    expertise: List[str] = Field(default=[], description="Areas of expertise")
    hourly_rate: Optional[Decimal]
    hours_per_week: Optional[int]
    students_assigned: int = Field(default=0, description="Number of students assigned")
    average_rating: Optional[Decimal] = Field(None, description="Average rating")
    completed_sessions: int = Field(default=0, description="Number of completed sessions")
    total_earnings: Optional[Decimal] = Field(None, description="Total earnings from institute")
    last_active: Optional[datetime] = Field(None, description="Last activity date")
    is_available: bool = Field(default=True, description="Currently available for new assignments")


class MentorPerformanceOut(BaseModel):
    """Mentor performance metrics"""
    mentor_id: UUID
    mentor_name: str
    total_sessions: int = Field(default=0, description="Total sessions conducted")
    completed_sessions: int = Field(default=0, description="Successfully completed sessions")
    cancelled_sessions: int = Field(default=0, description="Cancelled sessions")
    average_rating: Optional[Decimal] = Field(None, description="Average student rating")
    response_time_hours: Optional[float] = Field(None, description="Average response time in hours")
    student_satisfaction: Optional[float] = Field(None, description="Student satisfaction percentage")
    revenue_generated: Optional[Decimal] = Field(None, description="Revenue generated for institute")
    efficiency_score: Optional[float] = Field(None, description="Overall efficiency score")
    last_30_days_sessions: int = Field(default=0, description="Sessions in last 30 days")


class MentorAssignmentCreate(BaseModel):
    """Create mentor assignment to competition"""
    competition_id: UUID = Field(..., description="Competition to assign mentor to")
    assignment_message: Optional[str] = Field(None, description="Assignment instructions")
    estimated_hours: Optional[Decimal] = Field(None, description="Estimated hours required")
    deadline: Optional[datetime] = Field(None, description="Assignment deadline")
    special_instructions: Optional[str] = Field(None, description="Special instructions")


class MentorAssignmentOut(BaseModel):
    """Mentor assignment details"""
    id: UUID
    mentor_id: UUID
    mentor_name: str
    competition_id: UUID
    competition_title: str
    status: str = Field(..., description="Assignment status")
    assigned_at: datetime
    accepted_at: Optional[datetime]
    completed_at: Optional[datetime]
    estimated_hours: Optional[Decimal]
    actual_hours: Optional[Decimal]
    progress_percentage: float = Field(default=0.0, description="Completion percentage")
    questions_assigned: int = Field(default=0, description="Number of questions assigned")
    questions_checked: int = Field(default=0, description="Number of questions checked")


class MentorActivationRequest(BaseModel):
    """Request to activate/deactivate mentor"""
    is_active: bool = Field(..., description="Whether to activate or deactivate")
    reason: Optional[str] = Field(None, description="Reason for status change")
    effective_date: Optional[datetime] = Field(None, description="When change takes effect")


class MentorUpdateRequest(BaseModel):
    """Request to update mentor details"""
    hourly_rate: Optional[Decimal] = Field(None, description="Updated hourly rate")
    hours_per_week: Optional[int] = Field(None, description="Updated hours per week")
    expertise_areas: Optional[List[str]] = Field(None, description="Updated expertise areas")
    contract_terms: Optional[str] = Field(None, description="Updated contract terms")
    notes: Optional[str] = Field(None, description="Internal notes about mentor")


class MentorUtilizationReport(BaseModel):
    """Mentor utilization report"""
    mentor_id: UUID
    mentor_name: str
    allocated_hours: Decimal = Field(..., description="Hours allocated per week")
    actual_hours: Decimal = Field(..., description="Actual hours worked")
    utilization_percentage: float = Field(..., description="Utilization percentage")
    available_hours: Decimal = Field(..., description="Available hours remaining")
    efficiency_rating: Optional[float] = Field(None, description="Efficiency rating")
    period_start: datetime
    period_end: datetime


class MentorSatisfactionReport(BaseModel):
    """Mentor satisfaction report"""
    mentor_id: UUID
    mentor_name: str
    overall_satisfaction: float = Field(..., description="Overall satisfaction score")
    work_environment_rating: float = Field(..., description="Work environment rating")
    compensation_satisfaction: float = Field(..., description="Compensation satisfaction")
    support_rating: float = Field(..., description="Institute support rating")
    would_recommend: bool = Field(..., description="Would recommend institute to others")
    feedback_comments: Optional[str] = Field(None, description="Additional feedback")
    survey_date: datetime


class MentorPerformanceReportResponse(BaseModel):
    """Response for mentor performance report"""
    mentors: List[MentorPerformanceOut]
    summary: Dict[str, Any] = Field(..., description="Summary statistics")
    period_start: datetime
    period_end: datetime
    total_mentors: int
    active_mentors: int


class MentorApplicationsSummaryOut(BaseModel):
    """Summary of mentor applications"""
    total_applications: int
    pending_applications: int
    approved_applications: int
    rejected_applications: int
    approval_rate: float = Field(..., description="Approval rate percentage")
    average_response_time_days: float = Field(..., description="Average response time in days")
    top_expertise_areas: List[Dict[str, Any]] = Field(..., description="Most requested expertise areas")
    monthly_applications: List[Dict[str, Any]] = Field(..., description="Applications by month")


class InstituteMentorsResponse(BaseModel):
    """Response for institute mentors list"""
    data: List[InstituteMentorOut]
    total: int
    active: int
    inactive: int
    pending: int
