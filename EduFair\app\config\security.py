from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>earer
from passlib.context import <PERSON><PERSON><PERSON><PERSON>xt
from datetime import datetime, timedelta
from jose import jwt
from .config import settings
from passlib.exc import UnknownHashError

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="signin")

# Password context for hashing and verifying passwords
pwd_context = CryptContext(
    schemes=["bcrypt"],
    deprecated="auto"
)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)



def create_access_token(data: dict) -> str:
    """
    Creates a JWT access token.

    Args:
        data (dict): The data to include in the token.

    Returns:
        str: The encoded JWT token.
    """
    to_encode = data.copy()
    expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
    return encoded_jwt