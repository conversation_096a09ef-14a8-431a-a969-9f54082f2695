"""
Migration script to simplify MentorProfile model
- Remove unnecessary fields
- Create many-to-many tables for subjects
- Migrate existing data where possible
"""

from sqlalchemy import text
from sqlalchemy.orm import Session
from database import engine
import json


def migrate_mentor_profile():
    """Migrate mentor profile to simplified structure"""
    
    with Session(engine) as session:
        try:
            # Create new many-to-many tables
            session.execute(text("""
                CREATE TABLE IF NOT EXISTS mentor_expertise_subjects (
                    mentor_profile_id UUID REFERENCES mentor_profile(user_id) ON DELETE CASCADE,
                    subject_id UUID REFERENCES subjects(id) ON DELETE CASCADE,
                    PRIMARY KEY (mentor_profile_id, subject_id)
                );
            """))
            
            session.execute(text("""
                CREATE TABLE IF NOT EXISTS mentor_preferred_subjects (
                    mentor_profile_id UUID REFERENCES mentor_profile(user_id) ON DELETE CASCADE,
                    subject_id UUID REFERENCES subjects(id) ON DELETE CASCADE,
                    PRIMARY KEY (mentor_profile_id, subject_id)
                );
            """))
            
            # Backup existing data before dropping columns
            session.execute(text("""
                CREATE TABLE IF NOT EXISTS mentor_profile_backup AS 
                SELECT * FROM mentor_profile;
            """))
            
            # Remove columns that are no longer needed
            columns_to_drop = [
                'full_name',
                'expertise_areas', 
                'education',
                'certifications',
                'phone',
                'linkedin_url',
                'website',
                'current_position',
                'current_organization',
                'preferred_subjects',
                'is_verified',
                'verification_status',
                'verification_notes',
                'verified_at',
                'verified_by',
                'resume_url',
                'portfolio_url',
                'rating',
                'total_reviews',
                'competitions_checked'
            ]
            
            for column in columns_to_drop:
                try:
                    session.execute(text(f"ALTER TABLE mentor_profile DROP COLUMN IF EXISTS {column};"))
                except Exception as e:
                    print(f"Warning: Could not drop column {column}: {e}")
            
            session.commit()
            print("Migration completed successfully!")
            
        except Exception as e:
            session.rollback()
            print(f"Migration failed: {e}")
            raise


def rollback_migration():
    """Rollback the migration if needed"""
    
    with Session(engine) as session:
        try:
            # Drop the new tables
            session.execute(text("DROP TABLE IF EXISTS mentor_expertise_subjects;"))
            session.execute(text("DROP TABLE IF EXISTS mentor_preferred_subjects;"))
            
            # Restore from backup (if exists)
            session.execute(text("""
                DROP TABLE IF EXISTS mentor_profile;
                ALTER TABLE mentor_profile_backup RENAME TO mentor_profile;
            """))
            
            session.commit()
            print("Rollback completed successfully!")
            
        except Exception as e:
            session.rollback()
            print(f"Rollback failed: {e}")
            raise


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "rollback":
        rollback_migration()
    else:
        migrate_mentor_profile()
