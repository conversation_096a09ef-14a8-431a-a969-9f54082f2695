from pydantic import BaseModel, computed_field, Field
from uuid import UUID
from datetime import datetime
from typing import List, Optional
from enum import Enum
from config.config import settings


# Enums
class TaskStatusEnum(str, Enum):
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


# Base Models
class SubjectBase(BaseModel):
    name: str

    class Config:
        from_attributes = True


class ChapterBase(BaseModel):
    name: str
    description: str

    class Config:
        from_attributes = True


class TopicBase(BaseModel):
    name: str
    description: str

    class Config:
        from_attributes = True


class SubTopicBase(BaseModel):
    name: str
    description: str

    class Config:
        from_attributes = True


class TaskBase(BaseModel):
    name: str
    description: Optional[str] = None
    status: TaskStatusEnum = TaskStatusEnum.PENDING
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False

    class Config:
        from_attributes = True


# Response Models
class SubjectOut(SubjectBase):
    id: UUID

    class Config:
        from_attributes = True


class SubTopicOut(SubTopicBase):
    id: UUID
    topic_id: UUID

    class Config:
        from_attributes = True


class TopicOut(TopicBase):
    id: UUID
    chapter_id: UUID
    subtopics: List[SubTopicOut] = []

    class Config:
        from_attributes = True


class ChapterOut(ChapterBase):
    id: UUID
    subject_id: UUID
    topics: List[TopicOut] = []

    class Config:
        from_attributes = True


class TaskAttachmentOut(BaseModel):
    id: UUID
    file_url: str
    file_name: Optional[str] = None
    task_id: UUID
    student_id: Optional[UUID] = None  # NULL for teacher attachments, UUID for student submissions

    @computed_field
    @property
    def download_url(self) -> str:
        """Get the full download URL for the attachment"""
        return f"{settings.STATIC_FILES_URL}/{self.file_url}"

    class Config:
        from_attributes = True


class TaskStudentOut(BaseModel):
    task_id: UUID
    student_id: UUID
    submission_date: Optional[datetime] = None
    grade: Optional[int] = None

    class Config:
        from_attributes = True


class TaskClassroomOut(BaseModel):
    task_id: UUID
    classroom_id: UUID

    class Config:
        from_attributes = True


class TaskClassroomStudentOut(BaseModel):
    task_id: UUID
    classroom_id: UUID
    student_id: UUID

    class Config:
        from_attributes = True


class TaskChapterOut(BaseModel):
    task_id: UUID
    chapter_id: UUID

    class Config:
        from_attributes = True


class TaskTopicOut(BaseModel):
    task_id: UUID
    topic_id: UUID

    class Config:
        from_attributes = True


class TaskSubTopicOut(BaseModel):
    task_id: UUID
    subtopic_id: UUID

    class Config:
        from_attributes = True


# Main Task Response Model
class TaskOut(TaskBase):
    id: UUID
    subject_id: Optional[UUID] = None
    subject: Optional[SubjectOut] = None
    attachments: List[TaskAttachmentOut] = []
    students: List[TaskStudentOut] = []
    classrooms: List[TaskClassroomOut] = []
    classroom_students: List[TaskClassroomStudentOut] = []
    chapters: List[TaskChapterOut] = []
    topics: List[TaskTopicOut] = []
    subtopics: List[TaskSubTopicOut] = []
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Create Models
class SubjectCreate(SubjectBase):
    pass


class ChapterCreate(ChapterBase):
    subject_id: UUID


class TopicCreate(TopicBase):
    chapter_id: UUID


class SubTopicCreate(SubTopicBase):
    topic_id: UUID


class TaskCreate(TaskBase):
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []


class TaskCreateWithAssignments(TaskBase):
    """Create task with simultaneous assignments"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    classroom_ids: List[UUID] = []
    student_ids: List[UUID] = []
    classroom_student_assignments: List[dict] = []  # [{"classroom_id": UUID, "student_id": UUID}]


class TaskCreateForStudent(TaskBase):
    """Create task and assign to a single student"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    student_id: UUID


class TaskCreateForClassroom(TaskBase):
    """Create task and assign to a single classroom"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    classroom_id: UUID


class TaskCreateForMultipleStudents(TaskBase):
    """Create task and assign to multiple students"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    student_ids: List[UUID]


class TaskCreateForMultipleClassrooms(TaskBase):
    """Create task and assign to multiple classrooms"""
    subject_id: Optional[UUID] = None
    chapter_ids: List[UUID] = []
    topic_ids: List[UUID] = []
    subtopic_ids: List[UUID] = []
    classroom_ids: List[UUID]


# Update Models
class SubjectUpdate(BaseModel):
    name: Optional[str] = None

    class Config:
        from_attributes = True


class ChapterUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

    class Config:
        from_attributes = True


class TopicUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

    class Config:
        from_attributes = True


class SubTopicUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None

    class Config:
        from_attributes = True


class TaskUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    status: Optional[TaskStatusEnum] = None
    subject_id: Optional[UUID] = None
    deadline: Optional[datetime] = None
    accept_after_deadline: Optional[bool] = None

    class Config:
        from_attributes = True


# List Response Models
class SubjectListOut(BaseModel):
    subjects: List[SubjectOut]
    total: int

    class Config:
        from_attributes = True


class ChapterListOut(BaseModel):
    chapters: List[ChapterOut]
    total: int

    class Config:
        from_attributes = True


class TopicListOut(BaseModel):
    topics: List[TopicOut]
    total: int

    class Config:
        from_attributes = True


class SubTopicListOut(BaseModel):
    subtopics: List[SubTopicOut]
    total: int

    class Config:
        from_attributes = True


class TaskListOut(BaseModel):
    tasks: List[TaskOut]
    total: int

    class Config:
        from_attributes = True


class StudentTaskAttachmentOut(BaseModel):
    id: UUID
    file_url: str
    file_name: Optional[str] = None
    task_id: UUID
    student_id: UUID
    submission_date: Optional[datetime] = None

    @computed_field
    @property
    def download_url(self) -> str:
        """Get the full download URL for the attachment"""
        return f"{settings.STATIC_FILES_URL}/{self.file_url}"

    class Config:
        from_attributes = True


class StudentTaskMinimalOut(BaseModel):
    """Minimal task response for students - used in lists"""
    id: UUID
    name: str
    description: Optional[str] = None
    deadline: Optional[datetime] = None
    status: TaskStatusEnum
    accept_after_deadline: bool = False
    subject: Optional[SubjectOut] = None

    class Config:
        from_attributes = True


class StudentTaskDetailedOut(BaseModel):
    """Detailed task response for students - used for individual task view"""
    id: UUID
    name: str
    description: Optional[str] = None
    status: TaskStatusEnum
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False
    created_at: datetime
    updated_at: datetime
    subject: Optional[SubjectOut] = None
    chapters: List[ChapterOut] = []
    topics: List[TopicOut] = []
    subtopics: List[SubTopicOut] = []
    teacher_attachments: List[TaskAttachmentOut] = []
    my_attachments: List[StudentTaskAttachmentOut] = []

    class Config:
        from_attributes = True


class StudentTaskOut(BaseModel):
    """Student task response model with detailed information"""
    id: UUID
    name: str
    description: Optional[str] = None
    status: TaskStatusEnum
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False
    created_at: datetime
    updated_at: datetime
    subject_id: Optional[UUID] = None
    subject: Optional[SubjectOut] = None
    chapters: List[ChapterOut] = []
    topics: List[TopicOut] = []
    subtopics: List[SubTopicOut] = []

    class Config:
        from_attributes = True


class MyTasks(BaseModel):
    """Tasks response for students with their attachments"""
    tasks: List[StudentTaskOut]
    total: int
    student_attachments: List[StudentTaskAttachmentOut] = []

    class Config:
        from_attributes = True


class MyTasksMinimal(BaseModel):
    """Minimal tasks response for students"""
    tasks: List[StudentTaskMinimalOut]
    total: int

    class Config:
        from_attributes = True


# Specialized Response Models
class TaskWithDetailsOut(TaskOut):
    """Task with full details including nested subject, chapters, topics, subtopics, and attachments"""
    subject: Optional[SubjectOut] = None
    teacher_attachments: List[TaskAttachmentOut] = []
    student_attachments: List[StudentTaskAttachmentOut] = []
    # You can add more detailed nested objects here if needed

    class Config:
        from_attributes = True


class SubjectWithHierarchyOut(SubjectOut):
    """Subject with full chapter/topic/subtopic hierarchy"""
    chapters: List[ChapterOut] = []

    class Config:
        from_attributes = True


class ChapterWithTopicsOut(ChapterOut):
    """Chapter with topics and subtopics"""
    topics: List[TopicOut] = []

    class Config:
        from_attributes = True


class TopicWithSubTopicsOut(TopicOut):
    """Topic with subtopics"""
    subtopics: List[SubTopicOut] = []

    class Config:
        from_attributes = True


# ===== NEW SCHEMAS FOR TASK SUBMISSION AND GRADING =====

class TaskSubmissionCreate(BaseModel):
    """Schema for student task submission"""
    submission_text: Optional[str] = None  # Optional text submission
    submission_notes: Optional[str] = None  # Additional notes from student

    class Config:
        from_attributes = True


class TaskSubmissionOut(BaseModel):
    """Response schema for task submission"""
    task_id: UUID
    student_id: UUID
    submission_date: datetime
    submission_text: Optional[str] = None
    submission_notes: Optional[str] = None
    grade: Optional[int] = None
    attachments: List[StudentTaskAttachmentOut] = []  # All student attachments for this task

    class Config:
        from_attributes = True


class TaskSubmissionDetailOut(BaseModel):
    """Detailed response schema for task submission with all info"""
    task_id: UUID
    task_name: str
    task_description: Optional[str] = None
    student_id: UUID
    submission_date: datetime
    submission_text: Optional[str] = None
    submission_notes: Optional[str] = None
    grade: Optional[int] = None
    feedback: Optional[str] = None
    deadline: Optional[datetime] = None
    subject: Optional[SubjectOut] = None
    attachments: List[StudentTaskAttachmentOut] = []
    teacher_attachments: List[TaskAttachmentOut] = []  # Task materials from teacher

    class Config:
        from_attributes = True


class TaskGradeCreate(BaseModel):
    """Schema for grading a task"""
    grade: int = Field(..., ge=0, le=100, description="Grade between 0 and 100")
    feedback: Optional[str] = None

    class Config:
        from_attributes = True


class TaskGradeOut(BaseModel):
    """Response schema for task grade"""
    task_id: UUID
    student_id: UUID
    grade: int
    feedback: Optional[str] = None
    graded_at: datetime
    graded_by: UUID  # Teacher ID

    class Config:
        from_attributes = True


class StudentGradeOut(BaseModel):
    """Schema for student grade with task details"""
    task_id: UUID
    task_name: str
    task_description: Optional[str] = None
    subject: Optional[SubjectOut] = None
    grade: int
    feedback: Optional[str] = None
    submission_date: Optional[datetime] = None
    graded_at: datetime
    deadline: Optional[datetime] = None

    class Config:
        from_attributes = True


class StudentGradesListOut(BaseModel):
    """Response schema for list of student grades"""
    grades: List[StudentGradeOut]
    total: int

    class Config:
        from_attributes = True


class TaskAttachmentUploadOut(BaseModel):
    """Response schema for task attachment upload"""
    attachment_id: UUID
    file_name: str
    file_url: str
    download_url: str
    task_id: UUID
    student_id: UUID
    submission_date: datetime

    class Config:
        from_attributes = True


# Enhanced schemas for task editing
class ClassroomAssignmentOut(BaseModel):
    """Classroom assignment information for task editing"""
    classroom_id: UUID
    classroom_name: str

    class Config:
        from_attributes = True


class StudentAssignmentOut(BaseModel):
    """Individual student assignment information for task editing"""
    student_id: UUID
    student_name: str
    student_email: Optional[str] = None

    class Config:
        from_attributes = True


class TaskEditDetailsOut(BaseModel):
    """Enhanced task details for editing with assignment information"""
    id: UUID
    name: str
    description: Optional[str] = None
    status: TaskStatusEnum
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False
    created_at: datetime
    updated_at: datetime
    subject_id: Optional[UUID] = None
    subject: Optional[SubjectOut] = None

    # Assignment information
    assigned_to_classrooms: List[ClassroomAssignmentOut] = []
    assigned_to_students: List[StudentAssignmentOut] = []
    assignment_type: str  # "classroom", "individual", "mixed"

    # Attachments
    teacher_attachments: List[TaskAttachmentOut] = []

    # Academic hierarchy
    chapters: List[TaskChapterOut] = []
    topics: List[TaskTopicOut] = []
    subtopics: List[TaskSubTopicOut] = []

    class Config:
        from_attributes = True


class TaskEditUpdate(BaseModel):
    """Schema for updating task fields that teachers can modify"""
    deadline: Optional[datetime] = None
    accept_after_deadline: Optional[bool] = None

    # Assignment management
    add_classroom_ids: List[UUID] = []
    remove_classroom_ids: List[UUID] = []
    add_student_ids: List[UUID] = []
    remove_student_ids: List[UUID] = []

    class Config:
        from_attributes = True


class TeacherTaskMinimalOut(BaseModel):
    """Minimal task response for teachers - used in task lists to reduce server traffic"""
    id: UUID
    name: str
    description: Optional[str] = None
    status: TaskStatusEnum
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False
    created_at: datetime
    updated_at: datetime

    # Basic subject info only
    subject_name: Optional[str] = None

    # Assignment summary
    assignment_type: str  # "classroom", "individual", "mixed", "none"
    total_assigned_students: int = 0
    total_assigned_classrooms: int = 0

    # Submission summary
    total_submissions: int = 0
    pending_submissions: int = 0

    class Config:
        from_attributes = True


class TeacherTaskListOut(BaseModel):
    """Minimal task list response for teachers"""
    tasks: List[TeacherTaskMinimalOut]
    total: int

    class Config:
        from_attributes = True


# ===== ENHANCED SUBMISSION VIEWING AND GRADING SCHEMAS =====

class StudentSubmissionOut(BaseModel):
    """Individual student submission for teacher viewing"""
    student_id: UUID
    student_name: str
    student_email: str
    submission_date: Optional[datetime] = None
    submission_text: Optional[str] = None
    submission_notes: Optional[str] = None
    grade: Optional[int] = None
    feedback: Optional[str] = None
    graded_at: Optional[datetime] = None
    graded_by: Optional[UUID] = None
    graded_by_name: Optional[str] = None

    # Submission status
    is_submitted: bool = False
    is_graded: bool = False
    is_late: bool = False
    days_late: Optional[int] = None

    # Attachment count
    attachment_count: int = 0

    class Config:
        from_attributes = True


class TaskSubmissionsListOut(BaseModel):
    """List of all submissions for a task"""
    task_id: UUID
    task_name: str
    task_description: Optional[str] = None
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False
    subject_name: Optional[str] = None

    # Submission statistics
    total_assigned: int = 0
    total_submitted: int = 0
    total_graded: int = 0
    total_pending: int = 0
    total_late: int = 0

    # Individual submissions
    submissions: List[StudentSubmissionOut] = []

    class Config:
        from_attributes = True


class StudentSubmissionDetailOut(BaseModel):
    """Detailed view of a single student's submission for grading"""
    # Task information
    task_id: UUID
    task_name: str
    task_description: Optional[str] = None
    deadline: Optional[datetime] = None
    accept_after_deadline: bool = False
    subject: Optional[SubjectOut] = None

    # Student information
    student_id: UUID
    student_name: str  # This will be the username
    student_email: str

    # Submission details
    submission_date: Optional[datetime] = None
    submission_text: Optional[str] = None
    submission_notes: Optional[str] = None
    is_submitted: bool = False
    is_late: bool = False
    days_late: Optional[int] = None

    # Current grading
    grade: Optional[int] = None
    feedback: Optional[str] = None
    graded_at: Optional[datetime] = None
    graded_by: Optional[UUID] = None
    graded_by_name: Optional[str] = None
    is_graded: bool = False

    # Attachments
    student_attachments: List[StudentTaskAttachmentOut] = []
    teacher_attachments: List[TaskAttachmentOut] = []

    class Config:
        from_attributes = True


class BatchGradeCreate(BaseModel):
    """Schema for batch grading multiple submissions"""
    grades: List[dict] = Field(..., description="List of {student_id: UUID, grade: int, feedback: str}")

    class Config:
        from_attributes = True


class BatchGradeOut(BaseModel):
    """Response for batch grading operation"""
    task_id: UUID
    total_graded: int
    successful_grades: List[TaskGradeOut] = []
    failed_grades: List[dict] = []  # {student_id, error}

    class Config:
        from_attributes = True


class SubmissionFilterParams(BaseModel):
    """Filter parameters for submission listing"""
    status: Optional[str] = None  # "submitted", "pending", "graded", "late"
    grade_range_min: Optional[int] = None
    grade_range_max: Optional[int] = None
    submitted_after: Optional[datetime] = None
    submitted_before: Optional[datetime] = None

    class Config:
        from_attributes = True
