from typing import List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
from datetime import datetime

# Import CRUD functions
from Cruds.Events.Competitions import (
    create_competition_from_exam, create_competition_with_questions, get_competition_details,
    register_for_competition, get_competition_leaderboard, get_user_competition_result,
    get_available_exams_for_competition, start_competition_exam, get_competition_exam_questions,
    submit_competition_exam
)

# Import Schemas
from Schemas.Events.Events import EventOut

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type, require_verified_institute_or_teacher
from config.subscription_permission import require_competition_creation, update_usage_counter

router = APIRouter()


@router.post("/competitions/from-exam/{exam_id}", response_model=EventOut)
def create_competition_from_exam_route(
    exam_id: UUID,
    title: str,
    start_datetime: datetime,
    end_datetime: datetime,
    category_id: UUID,
    institute_id: UUID = None,
    description: str = None,
    short_description: str = None,
    banner_image_url: str = None,
    location_id: UUID = None,
    registration_start: datetime = None,
    registration_end: datetime = None,
    is_featured: bool = False,
    is_public: bool = True,
    requires_approval: bool = False,
    max_attendees: int = None,
    min_attendees: int = None,
    competition_rules: str = None,
    prize_details: dict = None,
    competition_duration_minutes: int = None,
    max_attempts: int = 1,
    passing_score: float = None,
    enable_proctoring: bool = False,
    enable_screen_recording: bool = False,
    enable_webcam: bool = False,
    disable_copy_paste: bool = True,
    randomize_questions: bool = True,
    auto_check_mcq: bool = True,
    require_mentor_check: bool = True,
    mentor_check_deadline: datetime = None,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_competition_creation())
):
    """Create a competition event based on an existing exam (with subscription validation)"""
    current_user = subscription_check["user"]

    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can create competitions")

    event_data = {
        "title": title,
        "description": description,
        "short_description": short_description,
        "banner_image_url": banner_image_url,
        "start_datetime": start_datetime,
        "end_datetime": end_datetime,
        "registration_start": registration_start,
        "registration_end": registration_end,
        "category_id": category_id,
        "location_id": location_id,
        "is_featured": is_featured,
        "is_public": is_public,
        "requires_approval": requires_approval,
        "max_attendees": max_attendees,
        "min_attendees": min_attendees,
        "competition_rules": competition_rules,
        "prize_details": prize_details,
        "competition_duration_minutes": competition_duration_minutes,
        "max_attempts": max_attempts,
        "passing_score": passing_score,
        "enable_proctoring": enable_proctoring,
        "enable_screen_recording": enable_screen_recording,
        "enable_webcam": enable_webcam,
        "disable_copy_paste": disable_copy_paste,
        "randomize_questions": randomize_questions,
        "auto_check_mcq": auto_check_mcq,
        "require_mentor_check": require_mentor_check,
        "mentor_check_deadline": mentor_check_deadline
    }

    # Create competition
    new_competition = create_competition_from_exam(db, exam_id, event_data, current_user.id, institute_id)

    # Update usage counter
    update_usage_counter(db, current_user, "competitions_created", 1)

    return new_competition


@router.post("/competitions/with-questions", response_model=EventOut)
def create_competition_with_questions_route(
    title: str,
    start_datetime: datetime,
    end_datetime: datetime,
    category_id: UUID,
    questions: List[Dict[str, Any]],
    institute_id: UUID = None,
    description: str = None,
    short_description: str = None,
    banner_image_url: str = None,
    location_id: UUID = None,
    registration_start: datetime = None,
    registration_end: datetime = None,
    is_featured: bool = False,
    is_public: bool = True,
    requires_approval: bool = False,
    max_attendees: int = None,
    min_attendees: int = None,
    competition_rules: str = None,
    prize_details: dict = None,
    competition_duration_minutes: int = None,
    max_attempts: int = 1,
    passing_score: float = None,
    enable_proctoring: bool = False,
    enable_screen_recording: bool = False,
    enable_webcam: bool = False,
    disable_copy_paste: bool = True,
    randomize_questions: bool = True,
    auto_check_mcq: bool = True,
    require_mentor_check: bool = True,
    mentor_check_deadline: datetime = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Create a competition event with custom questions (teachers and institutes)"""
    current_user = get_current_user(token, db)

    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can create competitions")

    if not questions or len(questions) == 0:
        raise HTTPException(status_code=400, detail="At least one question is required")

    event_data = {
        "title": title,
        "description": description,
        "short_description": short_description,
        "banner_image_url": banner_image_url,
        "start_datetime": start_datetime,
        "end_datetime": end_datetime,
        "registration_start": registration_start,
        "registration_end": registration_end,
        "category_id": category_id,
        "location_id": location_id,
        "is_featured": is_featured,
        "is_public": is_public,
        "requires_approval": requires_approval,
        "max_attendees": max_attendees,
        "min_attendees": min_attendees,
        "competition_rules": competition_rules,
        "prize_details": prize_details,
        "competition_duration_minutes": competition_duration_minutes,
        "max_attempts": max_attempts,
        "passing_score": passing_score,
        "enable_proctoring": enable_proctoring,
        "enable_screen_recording": enable_screen_recording,
        "enable_webcam": enable_webcam,
        "disable_copy_paste": disable_copy_paste,
        "randomize_questions": randomize_questions,
        "auto_check_mcq": auto_check_mcq,
        "require_mentor_check": require_mentor_check,
        "mentor_check_deadline": mentor_check_deadline
    }

    return create_competition_with_questions(db, event_data, questions, current_user.id, institute_id)


@router.get("/competitions/{event_id}/details")
def get_competition_details_route(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get detailed competition information"""
    current_user = get_current_user(token, db)
    return get_competition_details(db, event_id, current_user.id)


@router.get("/competitions/{event_id}/details/public")
def get_public_competition_details(
    event_id: UUID,
    db: Session = Depends(get_db)
):
    """Get public competition details (no authentication required)"""
    return get_competition_details(db, event_id)


@router.post("/competitions/{event_id}/register")
def register_for_competition_route(
    event_id: UUID,
    ticket_id: UUID = None,
    quantity: int = 1,
    attendee_info: dict = None,
    special_requirements: str = None,
    emergency_contact: dict = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Register for a competition"""
    current_user = get_current_user(token, db)
    
    registration_data = {
        "ticket_id": ticket_id,
        "quantity": quantity,
        "attendee_info": attendee_info,
        "special_requirements": special_requirements,
        "emergency_contact": emergency_contact
    }
    
    return register_for_competition(db, event_id, current_user.id, registration_data)


@router.get("/competitions/{event_id}/leaderboard")
def get_competition_leaderboard_route(
    event_id: UUID,
    limit: int = Query(50, ge=1, le=500, description="Number of entries to return"),
    db: Session = Depends(get_db)
):
    """Get competition leaderboard"""
    return {
        "event_id": str(event_id),
        "leaderboard": get_competition_leaderboard(db, event_id, limit),
        "last_updated": datetime.now()
    }


@router.get("/competitions/{event_id}/my-result")
def get_my_competition_result(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get current user's competition result"""
    current_user = get_current_user(token, db)
    return get_user_competition_result(db, event_id, current_user.id)


@router.get("/competitions/{event_id}/result/{user_id}")
def get_user_competition_result_route(
    event_id: UUID,
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Get specific user's competition result (teachers only)"""
    return get_user_competition_result(db, event_id, user_id)


@router.get("/competitions/available-exams")
def get_available_exams(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Get exams available for creating competitions"""
    current_user = get_current_user(token, db)
    return {
        "exams": get_available_exams_for_competition(db, current_user.id)
    }


@router.get("/competitions")
def get_competitions(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    category_id: UUID = Query(None, description="Filter by category"),
    status: str = Query(None, description="Filter by status"),
    db: Session = Depends(get_db)
):
    """Get all competitions"""
    from Models.Events import Event, EventStatusEnum
    from sqlalchemy.orm import joinedload
    
    query = db.query(Event).options(
        joinedload(Event.category),
        joinedload(Event.location)
    ).filter(
        Event.is_competition == True,
        Event.is_public == True
    )
    
    if category_id:
        query = query.filter(Event.category_id == category_id)
    
    if status:
        try:
            status_enum = EventStatusEnum(status)
            query = query.filter(Event.status == status_enum)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid status")
    
    competitions = query.offset(skip).limit(limit).all()
    
    competition_list = []
    for comp in competitions:
        # Get registration count
        from Models.Events import EventRegistration, RegistrationStatusEnum
        registration_count = db.query(EventRegistration).filter(
            EventRegistration.event_id == comp.id,
            EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
        ).count()
        
        competition_list.append({
            "id": str(comp.id),
            "title": comp.title,
            "description": comp.short_description,
            "start_datetime": comp.start_datetime,
            "end_datetime": comp.end_datetime,
            "category": comp.category.name if comp.category else None,
            "location": comp.location.name if comp.location else None,
            "is_virtual": comp.location.is_virtual if comp.location else False,
            "banner_image_url": comp.banner_image_url,
            "is_featured": comp.is_featured,
            "status": comp.status.value,
            "registration_count": registration_count,
            "max_attendees": comp.max_attendees,
            "prize_details": comp.prize_details
        })
    
    return {
        "competitions": competition_list,
        "total": len(competition_list),
        "skip": skip,
        "limit": limit
    }


@router.get("/competitions/featured")
def get_featured_competitions(
    limit: int = Query(5, ge=1, le=20),
    db: Session = Depends(get_db)
):
    """Get featured competitions"""
    from Models.Events import Event, EventStatusEnum
    from datetime import timezone
    
    competitions = db.query(Event).filter(
        Event.is_competition == True,
        Event.is_featured == True,
        Event.is_public == True,
        Event.status == EventStatusEnum.PUBLISHED,
        Event.start_datetime > datetime.now(timezone.utc)
    ).order_by(Event.start_datetime).limit(limit).all()
    
    featured_competitions = []
    for comp in competitions:
        featured_competitions.append({
            "id": str(comp.id),
            "title": comp.title,
            "description": comp.short_description,
            "start_datetime": comp.start_datetime,
            "end_datetime": comp.end_datetime,
            "banner_image_url": comp.banner_image_url,
            "prize_details": comp.prize_details
        })
    
    return {
        "featured_competitions": featured_competitions
    }


@router.get("/competitions/my-competitions")
def get_my_competitions(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get competitions the user is registered for"""
    current_user = get_current_user(token, db)
    
    from Models.Events import Event, EventRegistration
    from sqlalchemy.orm import joinedload
    
    registrations = db.query(EventRegistration).options(
        joinedload(EventRegistration.event)
    ).filter(
        EventRegistration.user_id == current_user.id
    ).join(Event).filter(
        Event.is_competition == True
    ).all()
    
    my_competitions = []
    for reg in registrations:
        event = reg.event
        my_competitions.append({
            "event_id": str(event.id),
            "title": event.title,
            "start_datetime": event.start_datetime,
            "end_datetime": event.end_datetime,
            "registration_status": reg.status.value,
            "registered_at": reg.registered_at,
            "has_exam": event.competition_exam_id is not None
        })
    
    return {
        "my_competitions": my_competitions
    }


@router.get("/competitions/by-institute/{institute_id}")
def get_competitions_by_institute(
    institute_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: str = Query(None, description="Filter by status"),
    db: Session = Depends(get_db)
):
    """Get competitions organized by a specific institute"""
    from Models.Events import Event, EventStatusEnum
    from sqlalchemy.orm import joinedload

    query = db.query(Event).options(
        joinedload(Event.category),
        joinedload(Event.location)
    ).filter(
        Event.institute_id == institute_id,
        Event.is_competition == True,
        Event.is_public == True
    )

    if status:
        try:
            status_enum = EventStatusEnum(status)
            query = query.filter(Event.status == status_enum)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid status")

    competitions = query.offset(skip).limit(limit).all()

    competition_list = []
    for comp in competitions:
        # Get registration count
        from Models.Events import EventRegistration, RegistrationStatusEnum
        registration_count = db.query(EventRegistration).filter(
            EventRegistration.event_id == comp.id,
            EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
        ).count()

        competition_list.append({
            "id": str(comp.id),
            "title": comp.title,
            "description": comp.short_description,
            "start_datetime": comp.start_datetime,
            "end_datetime": comp.end_datetime,
            "category": comp.category.name if comp.category else None,
            "location": comp.location.name if comp.location else None,
            "is_virtual": comp.location.is_virtual if comp.location else False,
            "banner_image_url": comp.banner_image_url,
            "is_featured": comp.is_featured,
            "status": comp.status.value,
            "registration_count": registration_count,
            "max_attendees": comp.max_attendees,
            "prize_details": comp.prize_details,
            "competition_duration_minutes": comp.competition_duration_minutes,
            "enable_proctoring": comp.enable_proctoring
        })

    return {
        "institute_id": str(institute_id),
        "competitions": competition_list,
        "total": len(competition_list)
    }


@router.get("/competitions/my-institute-competitions")
def get_my_institute_competitions(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: str = Query(None, description="Filter by status"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get competitions organized by current institute"""
    current_user = get_current_user(token, db)
    return get_competitions_by_institute(current_user.id, skip, limit, status, db)


# Competition Exam Routes
@router.post("/competitions/{event_id}/start-exam")
def start_competition_exam_route(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Start exam for competition"""
    current_user = get_current_user(token, db)
    return start_competition_exam(db, event_id, current_user.id)


@router.get("/competitions/{event_id}/exam-questions")
def get_competition_exam_questions_route(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get exam questions for competition"""
    current_user = get_current_user(token, db)
    return {
        "questions": get_competition_exam_questions(db, event_id, current_user.id)
    }


@router.post("/competitions/{event_id}/submit-exam")
def submit_competition_exam_route(
    event_id: UUID,
    answers: List[Dict[str, Any]],
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Submit exam answers for competition"""
    current_user = get_current_user(token, db)
    return submit_competition_exam(db, event_id, current_user.id, answers)
