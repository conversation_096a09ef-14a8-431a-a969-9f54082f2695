"""
CRUD operations for email verification
"""

import uuid
import random
import string
from datetime import datetime, timezone, timedelta
from typing import Op<PERSON>
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception, Request
from Models.users import User, EmailVerificationToken
from services.email_service import email_service
import logging

logger = logging.getLogger(__name__)

def generate_verification_code() -> str:
    """
    Generate a 6-digit verification code

    Returns:
        str: 6-digit verification code
    """
    return ''.join(random.choices(string.digits, k=6))

def create_verification_token(
    db: Session,
    user_id: uuid.UUID,
    email: str,
    token_type: str = "email_verification",
    expires_in_minutes: int = 30,
    request: Optional[Request] = None
) -> EmailVerificationToken:
    """
    Create a new verification token
    
    Args:
        db: Database session
        user_id: User ID
        email: Email to verify
        token_type: Type of token (email_verification, password_reset)
        expires_in_minutes: Token expiration time in minutes
        request: FastAPI request object for IP and user agent
        
    Returns:
        EmailVerificationToken: Created token
    """
    
    # Invalidate any existing tokens for this user and email
    existing_tokens = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.user_id == user_id,
        EmailVerificationToken.email == email,
        EmailVerificationToken.token_type == token_type,
        EmailVerificationToken.is_used == False
    ).all()

    for token in existing_tokens:
        token.is_used = True
        token.used_at = datetime.now(timezone.utc)

    # Generate unique verification code
    verification_code = generate_verification_code()

    # Ensure code is unique (very unlikely collision, but let's be safe)
    while db.query(EmailVerificationToken).filter(
        EmailVerificationToken.verification_code == verification_code,
        EmailVerificationToken.is_used == False,
        EmailVerificationToken.expires_at > datetime.now(timezone.utc)
    ).first():
        verification_code = generate_verification_code()

    # Create new token
    token = EmailVerificationToken(
        user_id=user_id,
        email=email,
        verification_code=verification_code,
        token_type=token_type,
        expires_at=datetime.now(timezone.utc) + timedelta(minutes=expires_in_minutes),
        ip_address=request.client.host if request else None,
        user_agent=request.headers.get("user-agent") if request else None
    )
    
    db.add(token)
    db.commit()
    db.refresh(token)
    
    logger.info(f"Created {token_type} token for user {user_id}, email {email}")
    return token

def send_verification_email(
    db: Session,
    user_id: uuid.UUID,
    base_url: str,
    request: Optional[Request] = None
) -> bool:
    """
    Send verification email to user
    
    Args:
        db: Database session
        user_id: User ID
        base_url: Base URL for verification link
        request: FastAPI request object
        
    Returns:
        bool: True if email sent successfully
    """
    
    # Get user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Create verification token
    token = create_verification_token(
        db=db,
        user_id=user_id,
        email=user.email,
        token_type="email_verification",
        request=request
    )
    
    # Send email with verification code
    success = email_service.send_verification_email(
        to_email=user.email,
        username=user.username,
        verification_code=token.verification_code
    )
    
    if success:
        logger.info(f"Verification email sent to {user.email}")
    else:
        logger.error(f"Failed to send verification email to {user.email}")
    
    return success

def verify_email_code(db: Session, verification_code: str, user_id: uuid.UUID) -> dict:
    """
    Verify email using verification code

    Args:
        db: Database session
        verification_code: 6-digit verification code
        user_id: User ID for additional security

    Returns:
        dict: Result of verification
    """

    # Find token by verification code and user ID
    verification_token = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.verification_code == verification_code,
        EmailVerificationToken.user_id == user_id,
        EmailVerificationToken.token_type == "email_verification"
    ).first()
    
    if not verification_token:
        raise HTTPException(status_code=404, detail="Invalid verification token")
    
    # Check if token is valid
    if not verification_token.is_valid():
        if verification_token.is_used:
            raise HTTPException(status_code=400, detail="Verification token has already been used")
        else:
            raise HTTPException(status_code=400, detail="Verification token has expired")
    
    # Get user
    user = db.query(User).filter(User.id == verification_token.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Mark token as used
    verification_token.is_used = True
    verification_token.used_at = datetime.now(timezone.utc)
    
    # Update user email verification status
    user.is_email_verified = True
    
    db.commit()
    
    logger.info(f"Email verified for user {user.username} ({user.email})")
    
    return {
        "message": "Email verified successfully",
        "user_id": str(user.id),
        "email": user.email,
        "verified_at": verification_token.used_at.isoformat()
    }

def resend_verification_email(
    db: Session,
    user_id: uuid.UUID,
    base_url: str,
    request: Optional[Request] = None
) -> dict:
    """
    Resend verification email
    
    Args:
        db: Database session
        user_id: User ID
        base_url: Base URL for verification link
        request: FastAPI request object
        
    Returns:
        dict: Result of resend operation
    """
    
    # Get user
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Check if already verified
    if user.is_email_verified:
        raise HTTPException(status_code=400, detail="Email is already verified")
    
    # Check rate limiting (max 3 emails per hour)
    one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
    recent_tokens = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.user_id == user_id,
        EmailVerificationToken.token_type == "email_verification",
        EmailVerificationToken.created_at >= one_hour_ago
    ).count()
    
    if recent_tokens >= 3:
        raise HTTPException(
            status_code=429, 
            detail="Too many verification emails sent. Please wait before requesting another."
        )
    
    # Send verification email
    success = send_verification_email(db, user_id, base_url, request)
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to send verification email")
    
    return {
        "message": "Verification email sent successfully",
        "email": user.email
    }

def send_password_reset_email(
    db: Session,
    email: str,
    base_url: str,
    request: Optional[Request] = None
) -> dict:
    """
    Send password reset email
    
    Args:
        db: Database session
        email: User email
        base_url: Base URL for reset link
        request: FastAPI request object
        
    Returns:
        dict: Result of operation
    """
    
    # Get user by email
    user = db.query(User).filter(User.email == email).first()
    if not user:
        # Don't reveal if email exists or not for security
        return {
            "message": "If an account with this email exists, a password reset link has been sent."
        }
    
    # Create password reset token
    token = create_verification_token(
        db=db,
        user_id=user.id,
        email=user.email,
        token_type="password_reset",
        expires_in_minutes=30,
        request=request
    )
    
    # Send email with reset code
    success = email_service.send_password_reset_email(
        to_email=user.email,
        username=user.username,
        reset_code=token.verification_code
    )
    
    if success:
        logger.info(f"Password reset email sent to {user.email}")
    else:
        logger.error(f"Failed to send password reset email to {user.email}")
    
    return {
        "message": "If an account with this email exists, a password reset link has been sent."
    }

def verify_password_reset_code(db: Session, reset_code: str, email: str) -> EmailVerificationToken:
    """
    Verify password reset code

    Args:
        db: Database session
        reset_code: 6-digit reset code
        email: User email for additional security

    Returns:
        EmailVerificationToken: Valid token
    """

    # Find token by reset code and email
    verification_token = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.verification_code == reset_code,
        EmailVerificationToken.email == email,
        EmailVerificationToken.token_type == "password_reset"
    ).first()
    
    if not verification_token:
        raise HTTPException(status_code=404, detail="Invalid reset token")
    
    # Check if token is valid
    if not verification_token.is_valid():
        if verification_token.is_used:
            raise HTTPException(status_code=400, detail="Reset token has already been used")
        else:
            raise HTTPException(status_code=400, detail="Reset token has expired")
    
    return verification_token

def cleanup_expired_tokens(db: Session) -> int:
    """
    Clean up expired tokens (should be run periodically)
    
    Args:
        db: Database session
        
    Returns:
        int: Number of tokens cleaned up
    """
    
    now = datetime.now(timezone.utc)
    expired_tokens = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.expires_at < now
    ).all()
    
    count = len(expired_tokens)
    
    for token in expired_tokens:
        db.delete(token)
    
    db.commit()
    
    logger.info(f"Cleaned up {count} expired verification tokens")
    return count
