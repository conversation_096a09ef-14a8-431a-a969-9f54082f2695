from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone
import json

# Import Models
from Models.Events import Event, EventStatusEnum
from Models.Competitions import (
    CompetitionQuestion, CompetitionQuestionTypeEnum, CompetitionDifficultyEnum,
    CompetitionAnswer
)
from Models.users import User, UserTypeEnum

# Import Schemas
from Schemas.Events.Events import EventOut


class CompetitionQuestionCreate:
    def __init__(self, **data):
        self.question_text = data['question_text']
        self.question_type = data['question_type']
        self.difficulty = data.get('difficulty', 'medium')
        self.marks = data.get('marks', 1)
        self.image_url = data.get('image_url')
        self.code_snippet = data.get('code_snippet')
        self.expected_answer = data.get('expected_answer')
        self.options = data.get('options')
        self.correct_option = data.get('correct_option')
        self.subject = data.get('subject')
        self.topic = data.get('topic')
        self.tags = data.get('tags')
        self.estimated_time_minutes = data.get('estimated_time_minutes')


class CompetitionQuestionOut:
    def __init__(self, question):
        self.id = question.id
        self.competition_id = question.competition_id
        self.question_text = question.question_text
        self.question_type = question.question_type.value
        self.difficulty = question.difficulty.value
        self.marks = question.marks
        self.image_url = question.image_url
        self.code_snippet = question.code_snippet
        self.expected_answer = question.expected_answer
        self.options = question.options
        self.correct_option = question.correct_option
        self.subject = question.subject
        self.topic = question.topic
        self.tags = question.tags
        self.is_ai_generated = question.is_ai_generated
        self.ai_prompt = question.ai_prompt
        self.ai_model_used = question.ai_model_used
        self.question_order = question.question_order
        self.question_group = question.question_group
        self.estimated_time_minutes = question.estimated_time_minutes
        self.created_at = question.created_at
        self.updated_at = question.updated_at


def create_competition_question(
    db: Session,
    competition_id: uuid.UUID,
    question_data: CompetitionQuestionCreate,
    creator_id: uuid.UUID
) -> CompetitionQuestionOut:
    """Create a new competition question"""
    
    # Verify competition exists and user has permission
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    # Check if user has permission to add questions
    user = db.query(User).filter(User.id == creator_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Only organizer or institute can add questions
    if competition.organizer_id != creator_id and competition.institute_id != creator_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can add questions")
    
    # Get next question order
    max_order = db.query(func.max(CompetitionQuestion.question_order)).filter(
        CompetitionQuestion.competition_id == competition_id
    ).scalar() or 0
    
    # Create question
    question = CompetitionQuestion(
        competition_id=competition_id,
        question_text=question_data.question_text,
        question_type=CompetitionQuestionTypeEnum(question_data.question_type),
        difficulty=CompetitionDifficultyEnum(question_data.difficulty),
        marks=question_data.marks,
        image_url=question_data.image_url,
        code_snippet=question_data.code_snippet,
        expected_answer=question_data.expected_answer,
        options=question_data.options,
        correct_option=question_data.correct_option,
        subject=question_data.subject,
        topic=question_data.topic,
        tags=question_data.tags,
        question_order=max_order + 1,
        estimated_time_minutes=question_data.estimated_time_minutes,
        is_ai_generated=False
    )
    
    db.add(question)
    db.commit()
    db.refresh(question)
    
    return CompetitionQuestionOut(question)


def get_competition_questions(
    db: Session,
    competition_id: uuid.UUID,
    user_id: uuid.UUID = None,
    include_answers: bool = False
) -> List[CompetitionQuestionOut]:
    """Get questions for a competition"""
    
    # Verify competition exists
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    # Check access permissions
    if not competition.is_public:
        if not user_id or (competition.organizer_id != user_id and competition.institute_id != user_id):
            raise HTTPException(status_code=403, detail="Access denied")
    
    # Get questions
    questions = db.query(CompetitionQuestion).filter(
        CompetitionQuestion.competition_id == competition_id
    ).order_by(CompetitionQuestion.question_order).all()
    
    question_list = []
    for question in questions:
        question_out = CompetitionQuestionOut(question)
        
        # Hide sensitive information for participants
        if user_id and (competition.organizer_id != user_id and competition.institute_id != user_id):
            question_out.expected_answer = None
            question_out.correct_option = None
            question_out.ai_prompt = None
        
        question_list.append(question_out)
    
    return question_list


def update_competition_question(
    db: Session,
    question_id: uuid.UUID,
    question_data: Dict[str, Any],
    user_id: uuid.UUID
) -> CompetitionQuestionOut:
    """Update a competition question"""
    
    question = db.query(CompetitionQuestion).filter(
        CompetitionQuestion.id == question_id
    ).first()
    
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    
    # Verify competition and permissions
    competition = db.query(Event).filter(Event.id == question.competition_id).first()
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != user_id and competition.institute_id != user_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can update questions")
    
    # Check if competition has started
    now = datetime.now(timezone.utc)
    if competition.start_datetime and now >= competition.start_datetime:
        raise HTTPException(status_code=400, detail="Cannot update questions after competition has started")
    
    # Update question fields
    for field, value in question_data.items():
        if hasattr(question, field) and field not in ['id', 'competition_id', 'created_at']:
            if field in ['question_type', 'difficulty']:
                if field == 'question_type':
                    setattr(question, field, CompetitionQuestionTypeEnum(value))
                else:
                    setattr(question, field, CompetitionDifficultyEnum(value))
            else:
                setattr(question, field, value)
    
    db.commit()
    db.refresh(question)
    
    return CompetitionQuestionOut(question)


def delete_competition_question(
    db: Session,
    question_id: uuid.UUID,
    user_id: uuid.UUID
) -> bool:
    """Delete a competition question"""
    
    question = db.query(CompetitionQuestion).filter(
        CompetitionQuestion.id == question_id
    ).first()
    
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    
    # Verify competition and permissions
    competition = db.query(Event).filter(Event.id == question.competition_id).first()
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != user_id and competition.institute_id != user_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can delete questions")
    
    # Check if competition has started
    now = datetime.now(timezone.utc)
    if competition.start_datetime and now >= competition.start_datetime:
        raise HTTPException(status_code=400, detail="Cannot delete questions after competition has started")
    
    # Check if question has answers
    answers_count = db.query(CompetitionAnswer).filter(
        CompetitionAnswer.question_id == question_id
    ).count()
    
    if answers_count > 0:
        raise HTTPException(status_code=400, detail="Cannot delete question with existing answers")
    
    db.delete(question)
    db.commit()
    
    return True


def reorder_competition_questions(
    db: Session,
    competition_id: uuid.UUID,
    question_orders: List[Dict[str, Any]],
    user_id: uuid.UUID
) -> List[CompetitionQuestionOut]:
    """Reorder competition questions"""
    
    # Verify competition and permissions
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != user_id and competition.institute_id != user_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can reorder questions")
    
    # Check if competition has started
    now = datetime.now(timezone.utc)
    if competition.start_datetime and now >= competition.start_datetime:
        raise HTTPException(status_code=400, detail="Cannot reorder questions after competition has started")
    
    # Update question orders
    for order_data in question_orders:
        question_id = order_data['question_id']
        new_order = order_data['order']
        
        question = db.query(CompetitionQuestion).filter(
            CompetitionQuestion.id == question_id,
            CompetitionQuestion.competition_id == competition_id
        ).first()
        
        if question:
            question.question_order = new_order
    
    db.commit()
    
    # Return updated questions
    return get_competition_questions(db, competition_id, user_id)


def generate_ai_questions(
    db: Session,
    competition_id: uuid.UUID,
    prompt: str,
    num_questions: int,
    question_type: str,
    difficulty: str,
    subject: str,
    user_id: uuid.UUID
) -> List[CompetitionQuestionOut]:
    """Generate AI questions for competition (placeholder implementation)"""
    
    # Verify competition and permissions
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != user_id and competition.institute_id != user_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can generate questions")
    
    # Check if competition has started
    now = datetime.now(timezone.utc)
    if competition.start_datetime and now >= competition.start_datetime:
        raise HTTPException(status_code=400, detail="Cannot add questions after competition has started")
    
    # TODO: Implement actual AI question generation
    # For now, create placeholder questions
    generated_questions = []
    
    for i in range(num_questions):
        # Get next question order
        max_order = db.query(func.max(CompetitionQuestion.question_order)).filter(
            CompetitionQuestion.competition_id == competition_id
        ).scalar() or 0
        
        # Create AI-generated question
        question = CompetitionQuestion(
            competition_id=competition_id,
            question_text=f"AI Generated Question {i+1}: {prompt}",
            question_type=CompetitionQuestionTypeEnum(question_type),
            difficulty=CompetitionDifficultyEnum(difficulty),
            marks=1,
            subject=subject,
            question_order=max_order + i + 1,
            is_ai_generated=True,
            ai_prompt=prompt,
            ai_model_used="placeholder-model"
        )
        
        # Add MCQ options if question type is MCQ
        if question_type == "mcq":
            question.options = [
                "Option A (AI Generated)",
                "Option B (AI Generated)",
                "Option C (AI Generated)",
                "Option D (AI Generated)"
            ]
            question.correct_option = 0
        
        db.add(question)
        generated_questions.append(question)
    
    db.commit()
    
    # Refresh and return questions
    for question in generated_questions:
        db.refresh(question)
    
    return [CompetitionQuestionOut(q) for q in generated_questions]
