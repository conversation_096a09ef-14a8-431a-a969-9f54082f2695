from sqlalchemy import Column, String, DateTime
from sqlalchemy.dialects.postgresql import UUID
from .baseModel import BaseModel
from datetime import datetime
import uuid

class AdminActionLog(BaseModel):
    __tablename__ = "admin_action_logs"
    admin_id = Column(String, nullable=False)
    action = Column(String, nullable=False)
    session_id = Column(String, nullable=False)
    reason = Column(String, nullable=True)
    timestamp = Column(DateTime, default=datetime.utcnow)

class ReconnectionRequest(BaseModel):
    __tablename__ = "reconnection_requests"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(String, nullable=False)
    student_id = Column(String, nullable=False)
    exam_id = Column(String, nullable=False)
    reason = Column(String, nullable=False)
    status = Column(String, default="pending_approval")  # pending_approval, approved, denied
    requested_at = Column(DateTime, default=datetime.utcnow)
    approved_by = Column(String, nullable=True)
    approved_at = Column(DateTime, nullable=True)
    denied_by = Column(String, nullable=True)
    denied_at = Column(DateTime, nullable=True)
    teacher_reason = Column(String, nullable=True) 