from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime
from uuid import UUID

# --- Subject Performance Schemas ---
class SubjectPerformance(BaseModel):
    """Performance metrics for a specific subject"""
    subject_id: UUID
    subject_name: str
    total_exams: int = Field(..., ge=0, description="Total exams taken in this subject")
    average_score: float = Field(..., ge=0, le=100, description="Average percentage score")
    highest_score: float = Field(..., ge=0, le=100, description="Highest percentage score achieved")
    lowest_score: float = Field(..., ge=0, le=100, description="Lowest percentage score achieved")
    total_marks_obtained: int = Field(..., ge=0, description="Total marks obtained across all exams")
    total_marks_possible: int = Field(..., ge=0, description="Total marks possible across all exams")
    improvement_trend: str = Field(..., description="Improving, Declining, or Stable")
    last_exam_date: Optional[datetime] = Field(None, description="Date of last exam in this subject")
    rank_in_subject: Optional[int] = Field(None, description="Student's rank in this subject among peers")

class ChapterPerformance(BaseModel):
    """Performance metrics for a specific chapter within a subject"""
    chapter_id: UUID
    chapter_name: str
    subject_name: str
    total_questions: int = Field(..., ge=0, description="Total questions attempted in this chapter")
    correct_answers: int = Field(..., ge=0, description="Number of correct answers")
    accuracy_percentage: float = Field(..., ge=0, le=100, description="Accuracy percentage for this chapter")
    average_score: float = Field(..., ge=0, le=100, description="Average score in this chapter")
    difficulty_level: str = Field(..., description="Easy, Medium, or Hard based on performance")
    needs_improvement: bool = Field(..., description="Whether this chapter needs more focus")

# --- Ranking and Position Schemas ---
class StudentRanking(BaseModel):
    """Student's ranking information"""
    overall_rank: int = Field(..., gt=0, description="Overall rank among all students")
    total_students: int = Field(..., gt=0, description="Total number of students")
    percentile: float = Field(..., ge=0, le=100, description="Percentile ranking")
    class_rank: Optional[int] = Field(None, description="Rank within the student's class")
    total_class_students: Optional[int] = Field(None, description="Total students in the class")

class SubjectRanking(BaseModel):
    """Subject-wise ranking information"""
    subject_id: UUID
    subject_name: str
    rank: int = Field(..., gt=0, description="Rank in this subject")
    total_students: int = Field(..., gt=0, description="Total students who took exams in this subject")
    percentile: float = Field(..., ge=0, le=100, description="Percentile in this subject")
    average_score: float = Field(..., ge=0, le=100, description="Student's average score in this subject")
    subject_average: float = Field(..., ge=0, le=100, description="Overall average score for this subject")

# --- Comparative Analysis Schemas ---
class PeerComparison(BaseModel):
    """Comparison with peer performance"""
    student_average: float = Field(..., ge=0, le=100, description="Student's average score")
    peer_average: float = Field(..., ge=0, le=100, description="Peer group average score")
    performance_status: str = Field(..., description="Above Average, Average, or Below Average")
    improvement_needed: float = Field(..., description="Points needed to reach peer average")
    subjects_above_average: List[str] = Field(default_factory=list, description="Subjects where student is above average")
    subjects_below_average: List[str] = Field(default_factory=list, description="Subjects where student is below average")

class PerformanceTrend(BaseModel):
    """Performance trend over time"""
    period: str = Field(..., description="Time period (Last 30 days, Last 3 months, etc.)")
    trend_direction: str = Field(..., description="Improving, Declining, or Stable")
    trend_percentage: float = Field(..., description="Percentage change in performance")
    exam_scores: List[Dict[str, Any]] = Field(default_factory=list, description="List of exam scores with dates")
    best_performing_month: Optional[str] = Field(None, description="Month with best performance")
    worst_performing_month: Optional[str] = Field(None, description="Month with worst performance")

# --- Comprehensive Statistics Response ---
class StudentStatistics(BaseModel):
    """Comprehensive student statistics"""
    student_id: UUID
    student_name: str
    class_name: Optional[str] = None
    
    # Overall Performance
    total_exams_taken: int = Field(..., ge=0, description="Total number of exams taken")
    overall_average: float = Field(..., ge=0, le=100, description="Overall average percentage")
    total_marks_obtained: int = Field(..., ge=0, description="Total marks obtained across all exams")
    total_marks_possible: int = Field(..., ge=0, description="Total marks possible across all exams")
    
    # Rankings
    ranking: StudentRanking
    subject_rankings: List[SubjectRanking] = Field(default_factory=list, description="Rankings in each subject")
    
    # Subject Performance
    subject_performance: List[SubjectPerformance] = Field(default_factory=list, description="Performance in each subject")
    chapter_performance: List[ChapterPerformance] = Field(default_factory=list, description="Performance in each chapter")
    
    # Comparative Analysis
    peer_comparison: PeerComparison
    performance_trend: PerformanceTrend
    
    # Strengths and Weaknesses
    strongest_subjects: List[str] = Field(default_factory=list, description="Top performing subjects")
    weakest_subjects: List[str] = Field(default_factory=list, description="Subjects needing improvement")
    strongest_chapters: List[str] = Field(default_factory=list, description="Top performing chapters")
    weakest_chapters: List[str] = Field(default_factory=list, description="Chapters needing improvement")
    
    # Recent Activity
    recent_exam_performance: List[Dict[str, Any]] = Field(default_factory=list, description="Last 5 exam performances")
    last_updated: datetime = Field(default_factory=datetime.now, description="When statistics were last calculated")

# --- Request/Response Schemas ---
class StatisticsRequest(BaseModel):
    """Request for student statistics"""
    student_id: Optional[UUID] = Field(None, description="Student ID (optional for current user)")
    include_peer_comparison: bool = Field(True, description="Whether to include peer comparison")
    include_trends: bool = Field(True, description="Whether to include performance trends")
    time_period_days: int = Field(90, ge=30, le=365, description="Time period for analysis in days")

class StatisticsResponse(BaseModel):
    """Response containing student statistics"""
    success: bool
    message: str
    data: Optional[StudentStatistics] = None
    error_details: Optional[str] = None

# --- Quick Stats Schemas ---
class QuickStats(BaseModel):
    """Quick overview statistics for dashboard"""
    total_exams: int
    average_score: float
    current_rank: int
    total_students: int
    improvement_trend: str
    last_exam_score: Optional[float] = None
    next_exam_date: Optional[datetime] = None

class QuickStatsResponse(BaseModel):
    """Quick stats response"""
    success: bool
    data: Optional[QuickStats] = None
    message: str = "Quick statistics retrieved successfully"
