from typing import List, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
import bcrypt
import json

# Import Models
from Models.users import User, UserTypeEnum, MentorProfile, MentorInstituteAssociation, MentorInstituteInvite, MentorInstituteApplication, InstituteProfile
from Models.Competitions import CompetitionMentorAssignment

# Import Schemas
from Schemas.Institute.Mentor import (
    MentorRegistrationBase, MentorProfileUpdate, MentorVerificationUpdate,
    MentorUserOut, MentorDetailedOut, MentorProfileOut, MentorListOut, MentorListResponse,
    MentorSearchFilter, MentorStatsOut, MentorApplicationCreate, MentorApplicationOut,
    InstituteInvitationCreate, InstituteInvitationOut,
    MentorInstituteAssociationOut, MentorInstituteAssociationDetailedOut, MentorInstituteAssociationCreate,
    AssociationSearchFilter, InviteSearchFilter, ApplicationSearchFilter,
    InviteResponseCreate, ApplicationResponseCreate
)
from Schemas.Institute.Institute import InstituteListOut

# Import Utilities
from utils.image_utils import get_profile_image_data


def hash_password(password: str) -> str:
    """Hash password using bcrypt"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')


def register_mentor(db: Session, mentor_data: MentorRegistrationBase) -> MentorUserOut:
    """Register a new mentor"""
    
    # Check if username already exists
    existing_user = db.query(User).filter(User.username == mentor_data.username).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already exists")
    
    # Check if email already exists
    existing_email = db.query(User).filter(User.email == mentor_data.email).first()
    if existing_email:
        raise HTTPException(status_code=400, detail="Email already exists")
    
    # Check if mobile already exists
    existing_mobile = db.query(User).filter(User.mobile == mentor_data.mobile).first()
    if existing_mobile:
        raise HTTPException(status_code=400, detail="Mobile number already exists")
    
    # Create user account
    hashed_password = hash_password(mentor_data.password)
    
    user = User(
        username=mentor_data.username,
        email=mentor_data.email,
        mobile=mentor_data.mobile,
        password_hash=hashed_password,
        country=mentor_data.country,
        user_type=UserTypeEnum.mentor,
        is_email_verified=False,
        is_mobile_verified=True  # TEMPORARY FIX: Auto-verify mobile for mentors
    )
    
    db.add(user)
    db.flush()  # Get the user ID
    
    # Create mentor profile
    mentor_profile = MentorProfile(
        user_id=user.id,
        bio=mentor_data.bio,
        experience_years=mentor_data.experience_years,
        languages=json.dumps(mentor_data.languages) if mentor_data.languages else None,
        hourly_rate=mentor_data.hourly_rate,
        availability_hours=json.dumps(mentor_data.availability_hours) if mentor_data.availability_hours else None
    )

    db.add(mentor_profile)
    db.flush()  # Get the mentor profile ID

    # Add expertise subjects
    if mentor_data.expertise_subject_ids:
        from Models.users import Subject
        expertise_subjects = db.query(Subject).filter(Subject.id.in_(mentor_data.expertise_subject_ids)).all()
        mentor_profile.expertise_subjects.extend(expertise_subjects)

    # Add preferred subjects
    if mentor_data.preferred_subject_ids:
        from Models.users import Subject
        preferred_subjects = db.query(Subject).filter(Subject.id.in_(mentor_data.preferred_subject_ids)).all()
        mentor_profile.preferred_subjects.extend(preferred_subjects)

    db.commit()
    db.refresh(user)
    
    return MentorUserOut.model_validate(user)


def get_mentor_by_id(db: Session, mentor_id: uuid.UUID) -> MentorDetailedOut:
    """Get mentor by ID with detailed information"""

    try:
        user = db.query(User).options(
            joinedload(User.mentor_profile).joinedload(MentorProfile.expertise_subjects),
            joinedload(User.mentor_profile).joinedload(MentorProfile.preferred_subjects)
        ).filter(
            User.id == mentor_id,
            User.user_type == UserTypeEnum.mentor
        ).first()

        if not user:
            raise HTTPException(status_code=404, detail="Mentor not found")

        if not user.mentor_profile:
            # Create a basic mentor profile if it doesn't exist
            mentor_profile = MentorProfile(
                user_id=user.id,
                bio=None,
                experience_years=None,
                languages=json.dumps([]),  # Empty list as JSON string
                hourly_rate=None,
                availability_hours=json.dumps({})  # Empty dict as JSON string
            )
            db.add(mentor_profile)
            db.commit()
            db.refresh(mentor_profile)
            user.mentor_profile = mentor_profile

        # Get statistics
        total_competitions = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.mentor_id == mentor_id
        ).count()

        active_institutes = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.mentor_id == mentor_id,
            MentorInstituteAssociation.status == "active"
        ).count()

        # Convert mentor profile to schema with proper data transformation
        mentor_profile = user.mentor_profile

        # Parse JSON fields safely
        try:
            languages = json.loads(mentor_profile.languages) if mentor_profile.languages else []
        except (json.JSONDecodeError, TypeError):
            languages = []

        try:
            availability_hours = json.loads(mentor_profile.availability_hours) if mentor_profile.availability_hours else {}
        except (json.JSONDecodeError, TypeError):
            availability_hours = {}

        # Convert subject relationships to dictionaries
        expertise_subjects = []
        if hasattr(mentor_profile, 'expertise_subjects') and mentor_profile.expertise_subjects:
            for subject in mentor_profile.expertise_subjects:
                if subject:  # Additional safety check
                    expertise_subjects.append({
                        "id": str(subject.id),
                        "name": subject.name
                    })

        preferred_subjects = []
        if hasattr(mentor_profile, 'preferred_subjects') and mentor_profile.preferred_subjects:
            for subject in mentor_profile.preferred_subjects:
                if subject:  # Additional safety check
                    preferred_subjects.append({
                        "id": str(subject.id),
                        "name": subject.name
                    })

        # Get profile image data - prioritize user.profile_picture over mentor_profile.profile_image_url
        user_profile_picture = user.profile_picture
        mentor_profile_image_url = getattr(mentor_profile, 'profile_image_url', None)

        # Use user.profile_picture as the primary source, fallback to mentor_profile.profile_image_url
        primary_image_url = user_profile_picture or mentor_profile_image_url
        profile_image_data = get_profile_image_data(primary_image_url, None)

        # Sync mentor_profile.profile_image_url with user.profile_picture if they're different
        if user_profile_picture and mentor_profile_image_url != user_profile_picture:
            mentor_profile.profile_image_url = user_profile_picture
            db.commit()

        # Create profile data with properly formatted fields
        profile_data = MentorProfileOut(
            id=mentor_profile.id,
            user_id=mentor_profile.user_id,
            bio=mentor_profile.bio,
            experience_years=mentor_profile.experience_years,
            languages=languages,
            hourly_rate=mentor_profile.hourly_rate,
            availability_hours=availability_hours,
            profile_image_url=primary_image_url,  # Use the primary image URL (user.profile_picture)
            profile_image=profile_image_data,
            expertise_subjects=expertise_subjects,
            preferred_subjects=preferred_subjects,
            created_at=mentor_profile.created_at,
            updated_at=mentor_profile.updated_at
        )

        # Get user profile image data (same as profile image data for mentors)
        user_profile_image_data = profile_image_data

        # Create user data without the mentor_profile to avoid circular reference
        user_data = MentorUserOut(
            id=user.id,
            username=user.username,
            email=user.email,
            mobile=user.mobile,
            country=user.country or "",
            profile_picture=user.profile_picture,
            profile_image=user_profile_image_data,
            user_type=user.user_type.value if hasattr(user.user_type, 'value') else str(user.user_type),
            is_email_verified=user.is_email_verified,
            is_mobile_verified=user.is_mobile_verified,
            created_at=user.created_at,
            mentor_profile=profile_data
        )

        return MentorDetailedOut(
            user=user_data,
            profile=profile_data,
            total_competitions=total_competitions,
            active_institutes=active_institutes,
            average_rating=None,  # Will be calculated separately if needed
            verification_status="pending"  # Default status, will be updated if verification system exists
        )

    except Exception as e:
        print(f"Error in get_mentor_by_id: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error retrieving mentor profile: {str(e)}")


def get_mentors(
    db: Session,
    skip: int = 0,
    limit: int = 20,
    search: Optional[str] = None
) -> MentorListResponse:
    """Get mentors with filtering and pagination"""
    query = db.query(User).options(
        joinedload(User.mentor_profile).joinedload(MentorProfile.expertise_subjects),
        joinedload(User.mentor_profile).joinedload(MentorProfile.preferred_subjects)
    ).join(MentorProfile, MentorProfile.user_id == User.id).filter(User.user_type == UserTypeEnum.mentor)

    # Add search functionality
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                MentorProfile.bio.ilike(search_term)
            )
        )

    # Get total count for pagination
    total = query.count()

    # Get mentors with pagination
    mentors = query.offset(skip).limit(limit).all()

    # Convert to MentorListOut objects
    mentor_list = []
    for user in mentors:
        mentor_profile = user.mentor_profile

        # Get profile image data - prioritize user.profile_picture over mentor_profile.profile_image_url
        user_profile_picture = user.profile_picture
        mentor_profile_image_url = getattr(mentor_profile, 'profile_image_url', None)
        primary_image_url = user_profile_picture or mentor_profile_image_url

        # Get profile image data as base64
        profile_image_data = get_profile_image_data(primary_image_url, None) if primary_image_url else None

        # Get expertise areas from subjects
        expertise_areas = []
        if mentor_profile and hasattr(mentor_profile, 'expertise_subjects') and mentor_profile.expertise_subjects:
            expertise_areas = [subject.name for subject in mentor_profile.expertise_subjects if subject]

        # Parse languages from JSON
        languages = []
        if mentor_profile and mentor_profile.languages:
            try:
                languages = json.loads(mentor_profile.languages) if mentor_profile.languages else []
            except (json.JSONDecodeError, TypeError):
                languages = []

        # Create full name from username (could be enhanced with first_name/last_name if available)
        full_name = user.username

        mentor_list.append(MentorListOut(
            id=user.id,
            username=user.username,
            full_name=full_name,
            email=user.email,
            mobile=user.mobile,
            country=user.country,
            bio=mentor_profile.bio if mentor_profile else None,
            expertise_areas=expertise_areas,
            experience_years=mentor_profile.experience_years if mentor_profile else None,
            current_position=None,  # Not available in current schema
            hourly_rate=mentor_profile.hourly_rate if mentor_profile else None,
            languages=languages,
            rating=None,  # Not implemented yet
            is_verified=user.is_email_verified,
            verification_status="pending",  # Default status
            profile_image_url=primary_image_url,
            profile_image=profile_image_data,
            created_at=user.created_at
        ))

    # Calculate pagination info
    page = (skip // limit) + 1
    has_next = (skip + limit) < total
    has_prev = skip > 0

    return MentorListResponse(
        mentors=mentor_list,
        total=total,
        page=page,
        size=limit,
        has_next=has_next,
        has_prev=has_prev
    )
   
def update_mentor_profile(
    db: Session,
    mentor_id: uuid.UUID,
    profile_update: MentorProfileUpdate
) -> MentorDetailedOut:
    """Update mentor profile"""
    
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    profile = user.mentor_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Mentor profile not found")
    
    # Update profile fields
    update_data = profile_update.model_dump(exclude_unset=True)

    # Handle subject relationships separately
    expertise_subject_ids = update_data.pop('expertise_subject_ids', None)
    preferred_subject_ids = update_data.pop('preferred_subject_ids', None)

    for field, value in update_data.items():
        if hasattr(profile, field):
            # Handle JSON fields
            if field in ['languages'] and value:
                setattr(profile, field, json.dumps(value))
            elif field == 'availability_hours' and value:
                setattr(profile, field, json.dumps(value))
            else:
                setattr(profile, field, value)

    # Update expertise subjects
    if expertise_subject_ids is not None:
        from Models.users import Subject
        profile.expertise_subjects.clear()
        if expertise_subject_ids:
            expertise_subjects = db.query(Subject).filter(Subject.id.in_(expertise_subject_ids)).all()
            profile.expertise_subjects.extend(expertise_subjects)

    # Update preferred subjects
    if preferred_subject_ids is not None:
        from Models.users import Subject
        profile.preferred_subjects.clear()
        if preferred_subject_ids:
            preferred_subjects = db.query(Subject).filter(Subject.id.in_(preferred_subject_ids)).all()
            profile.preferred_subjects.extend(preferred_subjects)

    db.commit()
    db.refresh(user)
    
    return get_mentor_by_id(db, mentor_id)


def verify_mentor(
    db: Session,
    mentor_id: uuid.UUID,
    verification_update: MentorVerificationUpdate,
    admin_id: uuid.UUID
) -> MentorDetailedOut:
    """Verify or reject mentor (admin only)"""
    
    user = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    profile = user.mentor_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Mentor profile not found")
    
    # Update verification status
    profile.verification_status = verification_update.verification_status.value
    profile.verification_notes = verification_update.verification_notes
    profile.verified_by = admin_id
    
    if verification_update.verification_status.value == "approved":
        profile.is_verified = True
        profile.verified_at = datetime.now(timezone.utc)
    else:
        profile.is_verified = False
        profile.verified_at = None
    
    db.commit()
    db.refresh(user)
    
    return get_mentor_by_id(db, mentor_id)


# Mentor-Institute Association Functions
def apply_to_institute(
    db: Session,
    mentor_id: uuid.UUID,
    application: MentorApplicationCreate
) -> MentorApplicationOut:
    """Mentor applies to join an institute"""

    # Verify mentor exists and is verified
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # TEMPORARY FIX: Only require email verification, remove profile verification requirement
    if not mentor.is_email_verified:
        raise HTTPException(status_code=400, detail="Mentor must verify email before applying to institutes")

    # REMOVED: Profile verification requirement for applications (temporary fix)
    # if not mentor.mentor_profile.is_verified:
    #     raise HTTPException(status_code=400, detail="Mentor must be verified to apply to institutes")

    # Verify institute exists and is verified
    institute = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == application.institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    if not institute.institute_profile.is_verified:
        raise HTTPException(status_code=400, detail="Institute must be verified to accept applications")

    # Check if application already exists
    existing_application = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.mentor_id == mentor_id,
        MentorInstituteApplication.institute_id == application.institute_id,
        MentorInstituteApplication.status == "pending"
    ).first()

    if existing_application:
        raise HTTPException(status_code=400, detail="Pending application already exists")

    # Check if active association already exists
    existing_association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == mentor_id,
        MentorInstituteAssociation.institute_id == application.institute_id,
        MentorInstituteAssociation.status == "active"
    ).first()

    if existing_association:
        raise HTTPException(status_code=400, detail="Active association already exists")

    # Create application
    app = MentorInstituteApplication(
        mentor_id=mentor_id,
        institute_id=application.institute_id,
        application_message=application.application_message,
        status="pending",
        proposed_hourly_rate=application.proposed_hourly_rate,
        availability_hours=application.availability_hours,
        applied_at=datetime.now(timezone.utc)
    )

    db.add(app)
    db.commit()
    db.refresh(app)

    return MentorApplicationOut.model_validate(app)


def invite_mentor(
    db: Session,
    institute_id: uuid.UUID,
    invitation: InstituteInvitationCreate
) -> InstituteInvitationOut:
    """Institute invites a mentor to join"""

    # Verify institute exists and is verified
    institute = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    if not institute.institute_profile.is_verified:
        raise HTTPException(status_code=400, detail="Institute must be verified to invite mentors")

    # Check if mentor exists by ID
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == invitation.mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # TEMPORARY FIX: Only require email verification, remove profile verification requirement
    if not mentor.is_email_verified:
        raise HTTPException(status_code=400, detail="Mentor must verify email before receiving invitations")

    # REMOVED: Profile verification requirement for invitations (temporary fix)
    # if not mentor.mentor_profile.is_verified:
    #     raise HTTPException(status_code=400, detail="Mentor must be verified to receive invitations")

    # Debug: Check total invitations in table
    total_invites = db.query(MentorInstituteInvite).count()
    print(f"DEBUG: Total invitations in table: {total_invites}")

    # Debug: Check invitations for this mentor
    mentor_invites = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.mentor_id == mentor.id
    ).count()
    print(f"DEBUG: Invitations for mentor {mentor.id}: {mentor_invites}")

    # Check if invite already exists
    existing_invite = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.mentor_id == mentor.id,
        MentorInstituteInvite.institute_id == institute_id,
        MentorInstituteInvite.status == "pending"
    ).first()

    if existing_invite:
        # Add more detailed error message for debugging
        raise HTTPException(
            status_code=400,
            detail=f"Pending invitation already exists for this mentor. Invite ID: {existing_invite.id}, Status: {existing_invite.status}"
        )

    # Check if active association already exists
    existing_association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == mentor.id,
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "active"
    ).first()

    if existing_association:
        raise HTTPException(status_code=400, detail="Active association already exists with this mentor")

    # Create invitation
    invite = MentorInstituteInvite(
        mentor_id=mentor.id,
        institute_id=institute_id,
        mentor_email=mentor.email,  # Use mentor's email from the User record
        invitation_message=invitation.invitation_message,
        status="pending",
        proposed_hourly_rate=invitation.proposed_hourly_rate,
        proposed_hours_per_week=invitation.proposed_hours_per_week,
        expertise_areas_needed=invitation.expertise_areas_needed,
        contract_terms=invitation.contract_terms,
        invited_at=datetime.now(timezone.utc),
        expires_at=datetime.now(timezone.utc).replace(hour=23, minute=59, second=59) + timedelta(days=30)
    )

    db.add(invite)
    db.commit()
    db.refresh(invite)

    return InstituteInvitationOut.model_validate(invite)


def respond_to_mentor_application_by_institute(
        db: Session,
        institute_id: uuid.UUID,
        application_id: uuid.UUID,
        response: ApplicationResponseCreate
) -> MentorInstituteAssociationOut:
    """Institute responds to a mentor's application"""

    # Find the pending application
    application = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.id == application_id,
        MentorInstituteApplication.institute_id == institute_id,
        MentorInstituteApplication.status == "pending"
    ).first()

    if not application:
        raise HTTPException(status_code=404, detail="Application not found or already processed")

    if response.accept:
        # Update application status
        application.status = "accepted"
        application.responded_at = datetime.now(timezone.utc)
        application.response_message = response.response_message

        # Create association
        association = MentorInstituteAssociation(
            mentor_id=application.mentor_id,
            institute_id=institute_id,
            status="active",
            hourly_rate=response.approved_hourly_rate or application.proposed_hourly_rate,
            hours_per_week=response.approved_hours_per_week or application.availability_hours,
            contract_terms=response.contract_terms,
            start_date=datetime.now(timezone.utc),
            created_from_application_id=application.id
        )

        db.add(association)
        db.commit()
        db.refresh(association)

        return MentorInstituteAssociationOut.model_validate(association)
    else:
        # Reject the application
        application.status = "rejected"
        application.responded_at = datetime.now(timezone.utc)
        application.response_message = response.response_message
        db.commit()

        raise HTTPException(status_code=400, detail="Application rejected")

def reject_mentor_request_by_institute(
        db: Session,
        institute_id: uuid.UUID,
        association_id: uuid.UUID,
        message: Optional[str] = None
):
    """Institute rejects a mentor's application"""
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.id == association_id,
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "pending"
    ).first()

    if not association:
        raise HTTPException(status_code=404, detail="Association not found or already processed")

    association.status = "rejected"
    association.response_message = message
    db.commit()
    db.refresh(association)

    return MentorInstituteAssociationOut.model_validate(association)


def accept_institute_invitation_by_mentor(
        db: Session,
        mentor_id: uuid.UUID,
        invite_id: uuid.UUID,
        response: InviteResponseCreate
) -> MentorInstituteAssociationOut:
    """Mentor accepts an institute's invitation and creates an association"""

    # Find the pending invite
    invite = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.id == invite_id,
        MentorInstituteInvite.mentor_id == mentor_id,
        MentorInstituteInvite.status == "pending"
    ).first()

    if not invite:
        raise HTTPException(status_code=404, detail="Invitation not found or already processed")

    # Check if invite has expired
    if invite.expires_at and datetime.now(timezone.utc) > invite.expires_at:
        invite.status = "expired"
        db.commit()
        raise HTTPException(status_code=400, detail="Invitation has expired")

    if response.accept:
        # Update invite status
        invite.status = "accepted"
        invite.responded_at = datetime.now(timezone.utc)
        invite.response_message = response.response_message

        # Create association
        association = MentorInstituteAssociation(
            mentor_id=mentor_id,
            institute_id=invite.institute_id,
            status="active",
            hourly_rate=response.proposed_hourly_rate or invite.proposed_hourly_rate,
            hours_per_week=response.proposed_hours_per_week or invite.proposed_hours_per_week,
            contract_terms=invite.contract_terms,
            start_date=datetime.now(timezone.utc),
            created_from_invite_id=invite.id
        )

        db.add(association)
        db.commit()
        db.refresh(association)

        return MentorInstituteAssociationOut.model_validate(association)
    else:
        # Reject the invite
        invite.status = "rejected"
        invite.responded_at = datetime.now(timezone.utc)
        invite.response_message = response.response_message
        db.commit()

        raise HTTPException(status_code=400, detail="Invitation rejected")

# Functions to get lists of invites, applications, and associations

def get_institute_invites(
    db: Session,
    institute_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None
) -> List[InstituteInvitationOut]:
    """Get all invites sent by an institute"""

    query = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.institute_id == institute_id
    )

    if status_filter:
        query = query.filter(MentorInstituteInvite.status == status_filter)

    invites = query.offset(skip).limit(limit).all()
    return [InstituteInvitationOut.model_validate(invite) for invite in invites]


def get_mentor_applications(
    db: Session,
    institute_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None
) -> List[MentorApplicationOut]:
    """Get all applications received by an institute"""

    query = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.institute_id == institute_id
    )

    if status_filter:
        query = query.filter(MentorInstituteApplication.status == status_filter)

    applications = query.offset(skip).limit(limit).all()
    return [MentorApplicationOut.model_validate(app) for app in applications]


def get_institute_associations(
    db: Session,
    institute_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None
) -> List[MentorInstituteAssociationOut]:
    """Get all active associations for an institute"""

    query = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id
    )

    if status_filter:
        query = query.filter(MentorInstituteAssociation.status == status_filter)

    associations = query.offset(skip).limit(limit).all()
    return [MentorInstituteAssociationOut.model_validate(assoc) for assoc in associations]


def get_mentor_associations(
    db: Session,
    mentor_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100,
    status_filter: Optional[str] = None
) -> List[MentorInstituteAssociationOut]:
    """Get all active associations for a mentor"""

    query = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == mentor_id
    )

    if status_filter:
        query = query.filter(MentorInstituteAssociation.status == status_filter)

    associations = query.offset(skip).limit(limit).all()
    return [MentorInstituteAssociationOut.model_validate(assoc) for assoc in associations]


def update_association_status(
    db: Session,
    association_id: uuid.UUID,
    new_status: str,
    updated_by: uuid.UUID
) -> MentorInstituteAssociationOut:
    """Update association status (e.g., active -> inactive -> terminated)"""

    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.id == association_id
    ).first()

    if not association:
        raise HTTPException(status_code=404, detail="Association not found")

    association.status = new_status
    association.updated_at = datetime.now(timezone.utc)

    if new_status == "terminated":
        association.end_date = datetime.now(timezone.utc)

    db.commit()
    db.refresh(association)

    return MentorInstituteAssociationOut.model_validate(association)