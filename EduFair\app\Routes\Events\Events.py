from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
import uuid

# Import CRUD functions
from Cruds.Events.Events import (
    create_event, get_events, get_event, update_event, delete_event,
    create_event_category, get_event_categories, get_event_category, update_event_category, delete_event_category,
    create_event_location, get_event_locations, get_event_location, update_event_location, delete_event_location,
    create_event_speaker, get_event_speakers, get_event_speaker, update_event_speaker, delete_event_speaker,
    create_event_ticket, get_event_tickets, update_event_ticket, delete_event_ticket,
    create_event_registration, get_user_registrations, get_event_registrations, cancel_event_registration,
    get_calendar_events, get_featured_events, get_upcoming_events, search_events,
    add_speaker_to_event, remove_speaker_from_event
)

# Import Schemas
from Schemas.Events.Events import (
    EventCreate, EventUpdate, EventOut, EventDetailedOut, EventMinimalOut,
    EventCategoryCreate, EventCategoryUpdate, EventCategoryOut,
    EventLocationCreate, EventLocationUpdate, EventLocationOut,
    EventSpeakerCreate, EventSpeakerUpdate, EventSpeakerOut,
    EventTicketCreate, EventTicketUpdate, EventTicketOut,
    EventRegistrationCreate, EventRegistrationOut, EventRegistrationDetailedOut,
    EventListFilter, EventListResponse, CalendarResponse
)

# Import dependencies
from config.session import get_db
from config.permission import require_type
from config.security import oauth2_scheme
from config.deps import get_current_user

router = APIRouter()


# Public Event Routes (no authentication required)
@router.get("/public", response_model=EventListResponse)
def get_public_events(
    category_id: Optional[UUID] = Query(None, description="Filter by category"),
    location_id: Optional[UUID] = Query(None, description="Filter by location"),
    is_featured: Optional[bool] = Query(None, description="Filter featured events"),
    is_competition: Optional[bool] = Query(None, description="Filter competition events"),
    search: Optional[str] = Query(None, description="Search term"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db)
):
    """Get public events with filtering and pagination"""
    filters = EventListFilter(
        category_id=category_id,
        location_id=location_id,
        is_featured=is_featured,
        is_competition=is_competition,
        search=search,
        is_public=True
    )
    
    skip = (page - 1) * size
    return get_events(db, filters, skip, size)


@router.get("/public/{event_id}", response_model=EventDetailedOut)
def get_public_event(
    event_id: UUID,
    db: Session = Depends(get_db)
):
    """Get public event details"""
    return get_event(db, event_id)


@router.get("/featured", response_model=List[EventMinimalOut])
def get_featured_events_route(
    limit: int = Query(5, ge=1, le=20, description="Number of featured events"),
    db: Session = Depends(get_db)
):
    """Get featured events"""
    return get_featured_events(db, limit)


@router.get("/upcoming", response_model=List[EventMinimalOut])
def get_upcoming_events_route(
    category_id: Optional[UUID] = Query(None, description="Filter by category"),
    limit: int = Query(10, ge=1, le=50, description="Number of upcoming events"),
    db: Session = Depends(get_db)
):
    """Get upcoming events"""
    return get_upcoming_events(db, limit, category_id)


@router.get("/search", response_model=List[EventMinimalOut])
def search_events_route(
    q: str = Query(..., description="Search term"),
    limit: int = Query(20, ge=1, le=100, description="Number of results"),
    db: Session = Depends(get_db)
):
    """Search events"""
    return search_events(db, q, limit)


@router.get("/calendar", response_model=CalendarResponse)
def get_calendar_events_route(
    year: int = Query(..., description="Year"),
    month: int = Query(..., ge=1, le=12, description="Month"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get calendar events for a specific month"""
    current_user = get_current_user(token, db)
    return get_calendar_events(db, year, month, current_user.id)


# Event Categories Routes
@router.get("/categories", response_model=List[EventCategoryOut])
def get_categories(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    active_only: bool = Query(True, description="Show only active categories"),
    db: Session = Depends(get_db)
):
    """Get event categories"""
    return get_event_categories(db, skip, limit, active_only)


@router.post("/categories", response_model=EventCategoryOut)
def create_category(
    category: EventCategoryCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create event category (teachers only)"""
    return create_event_category(db, category)


@router.get("/categories/{category_id}", response_model=EventCategoryOut)
def get_category(
    category_id: UUID,
    db: Session = Depends(get_db)
):
    """Get event category by ID"""
    return get_event_category(db, category_id)


@router.put("/categories/{category_id}", response_model=EventCategoryOut)
def update_category(
    category_id: UUID,
    category_update: EventCategoryUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Update event category (teachers only)"""
    return update_event_category(db, category_id, category_update)


@router.delete("/categories/{category_id}")
def delete_category(
    category_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Delete event category (teachers only)"""
    delete_event_category(db, category_id)
    return {"message": "Category deleted successfully"}


# Event Locations Routes
@router.get("/locations", response_model=List[EventLocationOut])
def get_locations(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get event locations"""
    return get_event_locations(db, skip, limit)


@router.post("/locations", response_model=EventLocationOut)
def create_location(
    location: EventLocationCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create event location (teachers only)"""
    return create_event_location(db, location)


@router.get("/locations/{location_id}", response_model=EventLocationOut)
def get_location(
    location_id: UUID,
    db: Session = Depends(get_db)
):
    """Get event location by ID"""
    return get_event_location(db, location_id)


@router.put("/locations/{location_id}", response_model=EventLocationOut)
def update_location(
    location_id: UUID,
    location_update: EventLocationUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Update event location (teachers only)"""
    return update_event_location(db, location_id, location_update)


@router.delete("/locations/{location_id}")
def delete_location(
    location_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Delete event location (teachers only)"""
    delete_event_location(db, location_id)
    return {"message": "Location deleted successfully"}


# Event Speakers Routes
@router.get("/speakers", response_model=List[EventSpeakerOut])
def get_speakers(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    featured_only: bool = Query(False, description="Show only featured speakers"),
    db: Session = Depends(get_db)
):
    """Get event speakers"""
    return get_event_speakers(db, skip, limit, featured_only)


@router.post("/speakers", response_model=EventSpeakerOut)
def create_speaker(
    speaker: EventSpeakerCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create event speaker (teachers only)"""
    return create_event_speaker(db, speaker)


@router.get("/speakers/{speaker_id}", response_model=EventSpeakerOut)
def get_speaker(
    speaker_id: UUID,
    db: Session = Depends(get_db)
):
    """Get event speaker by ID"""
    return get_event_speaker(db, speaker_id)


@router.put("/speakers/{speaker_id}", response_model=EventSpeakerOut)
def update_speaker(
    speaker_id: UUID,
    speaker_update: EventSpeakerUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Update event speaker (teachers only)"""
    return update_event_speaker(db, speaker_id, speaker_update)


@router.delete("/speakers/{speaker_id}")
def delete_speaker(
    speaker_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Delete event speaker (teachers only)"""
    delete_event_speaker(db, speaker_id)
    return {"message": "Speaker deleted successfully"}


# Event Management Routes (Teachers only)
@router.post("/", response_model=EventOut)
def create_event_route(
    event: EventCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create event (teachers only)"""
    current_user = get_current_user(token, db)
    return create_event(db, event, current_user.id)


@router.get("/my-events", response_model=EventListResponse)
def get_my_events(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Get events organized by current teacher"""
    current_user = get_current_user(token, db)
    filters = EventListFilter()
    skip = (page - 1) * size
    return get_events(db, filters, skip, size, current_user.id)


@router.get("/{event_id}", response_model=EventDetailedOut)
def get_event_route(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get event details (authenticated users can see private events they organize)"""
    current_user = get_current_user(token, db)
    return get_event(db, event_id, current_user.id)


@router.put("/{event_id}", response_model=EventOut)
def update_event_route(
    event_id: UUID,
    event_update: EventUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Update event (organizer only)"""
    current_user = get_current_user(token, db)
    return update_event(db, event_id, event_update, current_user.id)


@router.delete("/{event_id}")
def delete_event_route(
    event_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Delete event (organizer only)"""
    current_user = get_current_user(token, db)
    delete_event(db, event_id, current_user.id)
    return {"message": "Event deleted successfully"}


# Event Tickets Routes
@router.get("/{event_id}/tickets", response_model=List[EventTicketOut])
def get_event_tickets_route(
    event_id: UUID,
    db: Session = Depends(get_db)
):
    """Get tickets for an event"""
    return get_event_tickets(db, event_id)


@router.post("/{event_id}/tickets", response_model=EventTicketOut)
def create_event_ticket_route(
    event_id: UUID,
    ticket: EventTicketCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Create event ticket (organizer only)"""
    current_user = get_current_user(token, db)
    ticket.event_id = event_id  # Ensure event_id matches URL
    return create_event_ticket(db, ticket, current_user.id)


@router.put("/tickets/{ticket_id}", response_model=EventTicketOut)
def update_event_ticket_route(
    ticket_id: UUID,
    ticket_update: EventTicketUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Update event ticket (organizer only)"""
    current_user = get_current_user(token, db)
    return update_event_ticket(db, ticket_id, ticket_update, current_user.id)


@router.delete("/tickets/{ticket_id}")
def delete_event_ticket_route(
    ticket_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Delete event ticket (organizer only)"""
    current_user = get_current_user(token, db)
    delete_event_ticket(db, ticket_id, current_user.id)
    return {"message": "Ticket deleted successfully"}


# Event Speaker Management Routes
@router.post("/{event_id}/speakers/{speaker_id}")
def add_speaker_to_event_route(
    event_id: UUID,
    speaker_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Add speaker to event (organizer only)"""
    current_user = get_current_user(token, db)
    add_speaker_to_event(db, event_id, speaker_id, current_user.id)
    return {"message": "Speaker added to event successfully"}


@router.delete("/{event_id}/speakers/{speaker_id}")
def remove_speaker_from_event_route(
    event_id: UUID,
    speaker_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Remove speaker from event (organizer only)"""
    current_user = get_current_user(token, db)
    remove_speaker_from_event(db, event_id, speaker_id, current_user.id)
    return {"message": "Speaker removed from event successfully"}


# Event Registration Routes
@router.post("/{event_id}/register", response_model=EventRegistrationOut)
def register_for_event(
    event_id: UUID,
    registration: EventRegistrationCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Register for an event"""
    current_user = get_current_user(token, db)
    registration.event_id = event_id  # Ensure event_id matches URL
    return create_event_registration(db, registration, current_user.id)


@router.get("/my-registrations", response_model=List[EventRegistrationDetailedOut])
def get_my_registrations(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get current user's event registrations"""
    current_user = get_current_user(token, db)
    return get_user_registrations(db, current_user.id, skip, limit)


@router.get("/{event_id}/registrations", response_model=List[EventRegistrationOut])
def get_event_registrations_route(
    event_id: UUID,
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Get registrations for an event (organizer only)"""
    current_user = get_current_user(token, db)
    return get_event_registrations(db, event_id, current_user.id, skip, limit)


@router.delete("/registrations/{registration_id}")
def cancel_registration(
    registration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Cancel event registration"""
    current_user = get_current_user(token, db)
    cancel_event_registration(db, registration_id, current_user.id)
    return {"message": "Registration cancelled successfully"}
