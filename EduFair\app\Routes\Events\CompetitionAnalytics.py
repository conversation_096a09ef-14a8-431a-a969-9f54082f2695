from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
from datetime import datetime, timezone

# Import CRUD functions
from Cruds.Events.CompetitionAnalytics import (
    calculate_competition_results, get_competition_leaderboard, get_competition_analytics,
    get_participant_detailed_result, export_competition_results, get_competition_comparison
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# Results Calculation
@router.post("/competitions/{competition_id}/calculate-results")
def calculate_results(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Calculate competition results (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can calculate results")
    
    return calculate_competition_results(db, competition_id, current_user.id)


# Leaderboard
@router.get("/competitions/{competition_id}/leaderboard")
def get_leaderboard(
    competition_id: UUID,
    limit: int = Query(50, ge=1, le=200, description="Number of top participants"),
    include_details: bool = Query(False, description="Include session details"),
    db: Session = Depends(get_db)
):
    """Get competition leaderboard (public)"""
    return {
        "leaderboard": get_competition_leaderboard(db, competition_id, limit, include_details)
    }


@router.get("/competitions/{competition_id}/leaderboard/top")
def get_top_performers(
    competition_id: UUID,
    top_n: int = Query(10, ge=1, le=50, description="Number of top performers"),
    db: Session = Depends(get_db)
):
    """Get top N performers in competition"""
    leaderboard = get_competition_leaderboard(db, competition_id, top_n, True)
    
    return {
        "competition_id": str(competition_id),
        "top_performers": leaderboard,
        "total_shown": len(leaderboard)
    }


# Analytics (Organizers Only)
@router.get("/competitions/{competition_id}/analytics")
def get_analytics(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get comprehensive competition analytics (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can view analytics")
    
    return get_competition_analytics(db, competition_id, current_user.id)


@router.get("/competitions/{competition_id}/analytics/summary")
def get_analytics_summary(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get competition analytics summary (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can view analytics")
    
    analytics = get_competition_analytics(db, competition_id, current_user.id)
    
    # Return only summary data
    return {
        "competition_id": str(competition_id),
        "overview": analytics["overview"],
        "score_statistics": analytics["score_statistics"],
        "security_analytics": analytics["security_analytics"],
        "grade_distribution": analytics["grade_distribution"]
    }


# Participant Results
@router.get("/competitions/{competition_id}/my-result")
def get_my_result(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get current user's detailed result"""
    current_user = get_current_user(token, db)
    
    return get_participant_detailed_result(db, competition_id, current_user.id, current_user.id)


@router.get("/competitions/{competition_id}/participants/{participant_id}/result")
def get_participant_result(
    competition_id: UUID,
    participant_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get specific participant's detailed result (organizers only)"""
    current_user = get_current_user(token, db)
    
    return get_participant_detailed_result(db, competition_id, participant_id, current_user.id)


# Export Results
@router.get("/competitions/{competition_id}/export")
def export_results(
    competition_id: UUID,
    format: str = Query("csv", description="Export format (csv, json, xlsx)"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Export competition results (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can export results")
    
    if format not in ["csv", "json", "xlsx"]:
        raise HTTPException(status_code=400, detail="Invalid export format")
    
    return export_competition_results(db, competition_id, current_user.id, format)


# Competition Comparison
@router.post("/competitions/compare")
def compare_competitions(
    competition_ids: List[UUID],
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Compare multiple competitions (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can compare competitions")
    
    if not competition_ids or len(competition_ids) < 2:
        raise HTTPException(status_code=400, detail="At least 2 competitions required for comparison")
    
    return get_competition_comparison(db, competition_ids, current_user.id)


# Performance Insights
@router.get("/competitions/{competition_id}/insights")
def get_performance_insights(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get performance insights and recommendations (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can view insights")
    
    analytics = get_competition_analytics(db, competition_id, current_user.id)
    
    # Generate insights based on analytics
    insights = []
    recommendations = []
    
    # Participation insights
    participation_rate = analytics["overview"]["participation_rate"]
    if participation_rate < 50:
        insights.append("Low participation rate detected")
        recommendations.append("Consider improving competition promotion and accessibility")
    elif participation_rate > 90:
        insights.append("Excellent participation rate")
        recommendations.append("Maintain current engagement strategies")
    
    # Pass rate insights
    pass_rate = analytics["overview"]["pass_rate"]
    if pass_rate < 30:
        insights.append("Low pass rate indicates high difficulty")
        recommendations.append("Consider reviewing question difficulty or providing additional preparation materials")
    elif pass_rate > 90:
        insights.append("Very high pass rate may indicate low difficulty")
        recommendations.append("Consider increasing question complexity for better differentiation")
    
    # Security insights
    security_score = analytics["security_analytics"]["security_score"]
    if security_score == "high":
        insights.append("High security violations detected")
        recommendations.append("Review proctoring settings and consider stricter monitoring")
    
    # Question difficulty insights
    question_analytics = analytics["question_analytics"]
    easy_questions = len([q for q in question_analytics if q["difficulty_level"] == "easy"])
    hard_questions = len([q for q in question_analytics if q["difficulty_level"] == "hard"])
    
    if easy_questions > len(question_analytics) * 0.7:
        insights.append("Most questions are too easy")
        recommendations.append("Add more challenging questions to improve assessment quality")
    elif hard_questions > len(question_analytics) * 0.7:
        insights.append("Most questions are very difficult")
        recommendations.append("Balance question difficulty with easier questions")
    
    # Time insights
    avg_time = analytics["time_statistics"]["average_time_minutes"]
    competition_duration = 120  # Default duration in minutes
    
    if avg_time < competition_duration * 0.5:
        insights.append("Participants finish much earlier than allocated time")
        recommendations.append("Consider reducing competition duration or adding more questions")
    elif avg_time > competition_duration * 0.9:
        insights.append("Participants use most of the allocated time")
        recommendations.append("Current time allocation seems appropriate")
    
    return {
        "competition_id": str(competition_id),
        "insights": insights,
        "recommendations": recommendations,
        "performance_score": {
            "participation": "excellent" if participation_rate > 80 else "good" if participation_rate > 60 else "needs_improvement",
            "difficulty": "balanced" if 30 <= pass_rate <= 80 else "too_easy" if pass_rate > 80 else "too_hard",
            "security": security_score,
            "overall": "excellent" if len(insights) <= 2 else "good" if len(insights) <= 4 else "needs_attention"
        },
        "generated_at": analytics.get("overview", {}).get("calculated_at")
    }


# Real-time Statistics
@router.get("/competitions/{competition_id}/live-stats")
def get_live_statistics(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get real-time competition statistics (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can view live statistics")
    
    from Models.Events import Event, EventRegistration
    from Models.Competitions import CompetitionSession, CompetitionAnswer
    from sqlalchemy import func
    
    # Verify competition exists and organizer has permission
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != current_user.id and competition.institute_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get live statistics
    total_registered = db.query(EventRegistration).filter(
        EventRegistration.event_id == competition_id
    ).count()
    
    active_sessions = db.query(CompetitionSession).filter(
        CompetitionSession.competition_id == competition_id,
        CompetitionSession.is_active == True
    ).count()
    
    submitted_sessions = db.query(CompetitionSession).filter(
        CompetitionSession.competition_id == competition_id,
        CompetitionSession.is_submitted == True
    ).count()
    
    total_answers = db.query(CompetitionAnswer).filter(
        CompetitionAnswer.competition_id == competition_id
    ).count()
    
    flagged_sessions = db.query(CompetitionSession).filter(
        CompetitionSession.competition_id == competition_id,
        CompetitionSession.is_flagged == True
    ).count()
    
    return {
        "competition_id": str(competition_id),
        "live_stats": {
            "total_registered": total_registered,
            "active_participants": active_sessions,
            "completed_participants": submitted_sessions,
            "total_answers_submitted": total_answers,
            "flagged_sessions": flagged_sessions,
            "completion_rate": (submitted_sessions / total_registered * 100) if total_registered > 0 else 0
        },
        "competition_status": competition.status.value,
        "updated_at": datetime.now(timezone.utc).isoformat()
    }
