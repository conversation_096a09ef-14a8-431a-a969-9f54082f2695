import enum
from sqlalchemy import (
    Column, DateTime, Integer, String, <PERSON><PERSON><PERSON>, En<PERSON>, ForeignKey
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel


class TaskStatus(enum.Enum):
    PENDING = 'pending'
    IN_PROGRESS = 'in_progress'
    COMPLETED = 'completed'
    CANCELLED = 'cancelled'


class Task(BaseModel):
    __tablename__ = 'tasks'

    id = Column(UUID(as_uuid=True), primary_key=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    status = Column(Enum(TaskStatus), default=TaskStatus.PENDING, nullable=False)
    subject_id = Column(UUID(as_uuid=True), ForeignKey('subjects.id'), nullable=True)
    deadline = Column(DateTime, nullable=True)
    accept_after_deadline = Column(Boolean, default=False, nullable=False)

    # Relationships
    subject = relationship('Subject', back_populates='tasks')
    attachments = relationship('TaskAttachment', back_populates='task', cascade='all, delete-orphan')
    student_attachments = relationship('StudentTaskAttachment', back_populates='task', cascade='all, delete-orphan')
    students = relationship('TaskStudents', back_populates='task', cascade='all, delete-orphan')
    classrooms = relationship('TaskClassroom', back_populates='task', cascade='all, delete-orphan')
    classroom_students = relationship('TaskClassroomStudent', back_populates='task', cascade='all, delete-orphan')
    chapters = relationship('TaskChapter', back_populates='task', cascade='all, delete-orphan')
    topics = relationship('TaskTopic', back_populates='task', cascade='all, delete-orphan')
    subtopics = relationship('TaskSubTopic', back_populates='task', cascade='all, delete-orphan')

class TaskAttachment(BaseModel):
    __tablename__ = 'task_attachments'

    id = Column(UUID(as_uuid=True), primary_key=True)
    file_url = Column(String, nullable=False)
    file_name = Column(String, nullable=True)
    task_id = Column(UUID(as_uuid=True), ForeignKey('tasks.id'), nullable=False)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=True)  # NULL for teacher attachments, UUID for student submissions

    task = relationship('Task', back_populates='attachments')
    student = relationship('User', back_populates='task_attachments')


class StudentTaskAttachment(BaseModel):
    __tablename__ = 'student_task_attachments'

    id = Column(UUID(as_uuid=True), primary_key=True)
    file_url = Column(String, nullable=False)
    file_name = Column(String, nullable=True)
    task_id = Column(UUID(as_uuid=True), ForeignKey('tasks.id'), nullable=False)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    submission_date = Column(DateTime, nullable=True)

    task = relationship('Task', back_populates='student_attachments')
    student = relationship('User', back_populates='student_task_attachments')


class TaskStudents(BaseModel):
    __tablename__ = 'task_students'

    task_id = Column(UUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), primary_key=True)
    submission_date = Column(DateTime, nullable=True)
    grade = Column(Integer, nullable=True)
    submission_text = Column(String, nullable=True)  # Student's text submission
    submission_notes = Column(String, nullable=True)  # Additional notes from student
    feedback = Column(String, nullable=True)  # Teacher's feedback on the submission

    task = relationship('Task', back_populates='students')
    student = relationship('User', back_populates='tasks')


class TaskClassroom(BaseModel):
    __tablename__ = 'task_classrooms'

    task_id = Column(UUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True)
    classroom_id = Column(UUID(as_uuid=True), ForeignKey('classrooms.id'), primary_key=True)

    task = relationship('Task', back_populates='classrooms')
    classroom = relationship('Classroom', back_populates='tasks')


class TaskClassroomStudent(BaseModel):
    __tablename__ = 'task_classroom_students'

    task_id = Column(UUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True)
    classroom_id = Column(UUID(as_uuid=True), ForeignKey('classrooms.id'), primary_key=True)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), primary_key=True)

    task = relationship('Task', back_populates='classroom_students')
    classroom = relationship('Classroom', back_populates='task_students')
    student = relationship('User', back_populates='task_classrooms')

class TaskChapter(BaseModel):
    __tablename__ = 'task_chapters'

    task_id = Column(UUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True)
    chapter_id = Column(UUID(as_uuid=True), ForeignKey('chapters.id'), primary_key=True)

    task = relationship('Task', back_populates='chapters')
    chapter = relationship('Chapter', back_populates='tasks')

class TaskTopic(BaseModel):
    __tablename__ = 'task_topics'

    task_id = Column(UUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True)
    topic_id = Column(UUID(as_uuid=True), ForeignKey('topics.id'), primary_key=True)

    task = relationship('Task', back_populates='topics')
    topic = relationship('Topic', back_populates='tasks')

class TaskSubTopic(BaseModel):
    __tablename__ = 'task_subtopics'

    task_id = Column(UUID(as_uuid=True), ForeignKey('tasks.id'), primary_key=True)
    subtopic_id = Column(UUID(as_uuid=True), ForeignKey('subtopics.id'), primary_key=True)

    task = relationship('Task', back_populates='subtopics')
    subtopic = relationship('SubTopic', back_populates='tasks')