import uuid
from sqlalchemy.orm import Session
from fastapi import HTTPException
from Models.users import Subject
from Schemas.Token import Token
from Schemas.TeacherModule.Subjects import SubjectBase, SubjectCreate


def create_subject(db: Session, subject: SubjectCreate) -> SubjectBase:
    if db.query(Subject).filter(Subject.name == subject.name).first():
        raise HTTPException(status_code=400, detail="Subject already exists.")
    new_subject = Subject(
        id=uuid.uuid4(),
        name=subject.name
    )
    db.add(new_subject)
    db.commit()
    db.refresh(new_subject)
    return new_subject

def get_all_subjects(db: Session) -> list[SubjectBase]:
    subjects = db.query(Subject).all()
    return subjects if subjects else []

def get_subject_by_id(db: Session, subject_id: str) -> SubjectBase:
    subject = db.query(Subject).filter(Subject.id == subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found.")
    return subject

def delete_subject(db: Session, subject_id: str) -> SubjectBase:
    subject = db.query(Subject).filter(Subject.id == subject_id).first()
    if not subject:
        raise HTTPException(status_code=404, detail="Subject not found.")
    db.delete(subject)
    db.commit()
    return subject

