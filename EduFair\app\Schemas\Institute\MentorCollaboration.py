"""
Simplified schemas for mentor-institute collaboration
Easy-to-use schemas with clear naming and intuitive structure
"""

from pydantic import BaseModel, Field, EmailStr
from uuid import UUID
from typing import List, Optional
from datetime import datetime
from decimal import Decimal
from enum import Enum


# Status Enums with clear meanings
class InvitationStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    EXPIRED = "expired"


class ApplicationStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"


class CollaborationStatus(str, Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    ENDED = "ended"


# === INVITATION SCHEMAS ===

class SendInvitationRequest(BaseModel):
    """Send invitation to a mentor"""
    mentor_email: EmailStr = Field(..., description="Email of the mentor to invite")
    message: str = Field(..., min_length=10, max_length=500, description="Personal invitation message")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Proposed hourly rate (USD)")
    hours_per_week: Optional[int] = Field(None, ge=1, le=40, description="Expected hours per week")
    subjects_needed: Optional[List[str]] = Field(default=[], description="Subject areas needed")
    start_date: Optional[datetime] = Field(None, description="Proposed start date")
    
    class Config:
        json_schema_extra = {
            "example": {
                "mentor_email": "<EMAIL>",
                "message": "We'd love to have you join our institute as a mentor for our mathematics program.",
                "hourly_rate": 50.00,
                "hours_per_week": 10,
                "subjects_needed": ["Mathematics", "Physics"],
                "start_date": "2024-01-15T09:00:00Z"
            }
        }


class InvitationDetails(BaseModel):
    """Complete invitation details"""
    id: UUID
    mentor_email: str
    mentor_name: Optional[str] = None
    institute_name: str
    message: str
    status: InvitationStatus
    hourly_rate: Optional[Decimal]
    hours_per_week: Optional[int]
    subjects_needed: List[str]
    sent_at: datetime
    expires_at: datetime
    responded_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class RespondToInvitationRequest(BaseModel):
    """Mentor's response to an invitation"""
    accept: bool = Field(..., description="True to accept, False to decline")
    message: Optional[str] = Field(None, max_length=300, description="Response message")
    counter_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Counter-proposed hourly rate")
    counter_hours_per_week: Optional[int] = Field(None, ge=1, le=40, description="Counter-proposed hours per week")
    
    class Config:
        json_schema_extra = {
            "example": {
                "accept": True,
                "message": "I'm excited to join your team!",
                "counter_hourly_rate": 55.00,
                "counter_hours_per_week": 8
            }
        }


# === APPLICATION SCHEMAS ===

class SubmitApplicationRequest(BaseModel):
    """Mentor applies to join an institute"""
    institute_id: UUID = Field(..., description="Institute to apply to")
    message: str = Field(..., min_length=10, max_length=500, description="Application message")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Desired hourly rate (USD)")
    hours_available: Optional[int] = Field(None, ge=1, le=40, description="Hours available per week")
    subjects: Optional[List[str]] = Field(default=[], description="Subject areas you can teach")
    
    class Config:
        json_schema_extra = {
            "example": {
                "institute_id": "123e4567-e89b-12d3-a456-************",
                "message": "I would like to contribute to your institute's mission of quality education.",
                "hourly_rate": 45.00,
                "hours_available": 15,
                "subjects": ["Mathematics", "Computer Science"]
            }
        }


class ApplicationDetails(BaseModel):
    """Complete application details"""
    id: UUID
    mentor_id: UUID
    mentor_name: str
    mentor_email: str
    institute_name: str
    message: str
    status: ApplicationStatus
    hourly_rate: Optional[Decimal]
    hours_available: Optional[int]
    subjects: List[str]
    submitted_at: datetime
    reviewed_at: Optional[datetime] = None
    reviewer_message: Optional[str] = None
    
    class Config:
        from_attributes = True


class ReviewApplicationRequest(BaseModel):
    """Institute's review of a mentor application"""
    approve: bool = Field(..., description="True to approve, False to reject")
    message: Optional[str] = Field(None, max_length=300, description="Review message")
    approved_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Approved hourly rate")
    approved_hours_per_week: Optional[int] = Field(None, ge=1, le=40, description="Approved hours per week")
    
    class Config:
        json_schema_extra = {
            "example": {
                "approve": True,
                "message": "Welcome to our team! We're excited to work with you.",
                "approved_hourly_rate": 45.00,
                "approved_hours_per_week": 12
            }
        }


# === COLLABORATION SCHEMAS ===

class CollaborationDetails(BaseModel):
    """Active mentor-institute collaboration"""
    id: UUID
    mentor_id: UUID
    mentor_name: str
    mentor_email: str
    institute_id: UUID
    institute_name: str
    status: CollaborationStatus
    hourly_rate: Optional[Decimal]
    hours_per_week: Optional[int]
    subjects: List[str]
    started_at: datetime
    ended_at: Optional[datetime] = None
    total_hours_worked: Optional[Decimal] = Field(default=0, description="Total hours worked")
    total_earnings: Optional[Decimal] = Field(default=0, description="Total earnings")
    
    class Config:
        from_attributes = True


class UpdateCollaborationRequest(BaseModel):
    """Update collaboration details"""
    status: Optional[CollaborationStatus] = None
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    hours_per_week: Optional[int] = Field(None, ge=1, le=40)
    end_date: Optional[datetime] = None
    
    class Config:
        json_schema_extra = {
            "example": {
                "status": "paused",
                "hourly_rate": 50.00,
                "hours_per_week": 8
            }
        }


# === LIST RESPONSE SCHEMAS ===

class InvitationListResponse(BaseModel):
    """Paginated list of invitations"""
    invitations: List[InvitationDetails]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


class ApplicationListResponse(BaseModel):
    """Paginated list of applications"""
    applications: List[ApplicationDetails]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


class CollaborationListResponse(BaseModel):
    """Paginated list of collaborations"""
    collaborations: List[CollaborationDetails]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


# === SUMMARY SCHEMAS ===

class CollaborationSummary(BaseModel):
    """Summary statistics for mentor-institute collaborations"""
    total_active_collaborations: int
    total_pending_invitations: int
    total_pending_applications: int
    total_mentors: int
    average_hourly_rate: Optional[Decimal]
    total_hours_this_month: Optional[Decimal]
    
    class Config:
        json_schema_extra = {
            "example": {
                "total_active_collaborations": 15,
                "total_pending_invitations": 3,
                "total_pending_applications": 7,
                "total_mentors": 15,
                "average_hourly_rate": 47.50,
                "total_hours_this_month": 240.5
            }
        }
