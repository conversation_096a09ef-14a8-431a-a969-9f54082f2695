"""
Test script to verify institute logo and banner upload functionality.

This script tests:
1. Single logo upload endpoint
2. Single banner upload endpoint
3. Bulk upload with logo and banner in profile update
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import base64
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from main import app
from config.session import get_db
from Models.users import User, UserTypeEnum, InstituteProfile
from Schemas.Institute.Institute import InstituteProfileWithDocumentsRequest, ImageData
import uuid
import bcrypt

client = TestClient(app)

def create_test_institute_user(test_suffix=""):
    """Create a test institute user for testing"""
    try:
        db = next(get_db())
        
        # Use unique values for each test
        test_id = str(uuid.uuid4())[:8]
        test_email = f"test_logo_banner_{test_suffix}_{test_id}@example.com"
        test_username = f"test_logo_banner_{test_suffix}_{test_id}"
        test_mobile = f"+123456789{test_id[:1]}"
        
        # Check if test user already exists and delete
        existing_users = db.query(User).filter(
            (User.email == test_email) | 
            (User.username == test_username) | 
            (User.mobile == test_mobile)
        ).all()
        for user in existing_users:
            # Delete associated profile first
            profile = db.query(InstituteProfile).filter(InstituteProfile.user_id == user.id).first()
            if profile:
                db.delete(profile)
            db.delete(user)
        db.commit()
        
        # Create new test user
        hashed_password = bcrypt.hashpw("TestPass123!".encode('utf-8'), bcrypt.gensalt())
        
        user = User(
            id=uuid.uuid4(),
            username=test_username,
            email=test_email,
            mobile=test_mobile,
            password_hash=hashed_password.decode('utf-8'),
            country="United States",
            user_type=UserTypeEnum.institute,
            is_email_verified=True,
            is_mobile_verified=True
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print(f"✅ Created test institute user: {user.id}")
        
        # Generate auth token
        from config.security import create_access_token
        token = create_access_token(data={"sub": test_email})
        
        return token, user.id
        
    except Exception as e:
        print(f"❌ Error creating test institute user: {e}")
        db.rollback()
        return None, None
    finally:
        db.close()

def create_test_image_data(filename="test_logo.png"):
    """Create test image data in base64 format"""
    # Create a simple test image content (PNG header + minimal data)
    png_header = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    test_content = png_header + b'\x00' * 50  # Minimal PNG data
    
    return base64.b64encode(test_content).decode()

def test_single_logo_upload():
    """Test single logo upload endpoint"""
    print("🧪 Testing single logo upload endpoint...")
    
    # Create test user and get token
    token, user_id = create_test_institute_user("logo")
    if not token:
        return False
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # Create test image content
        test_content = create_test_image_data("test_logo.png")
        
        # Upload using single logo endpoint
        files = {"file": ("test_logo.png", base64.b64decode(test_content), "image/png")}
        
        response = client.post(
            "/api/files/institute-logo",
            headers=headers,
            files=files
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Logo upload successful!")
            print(f"   Logo URL: {result.get('logo_url')}")
            print(f"   Thumbnail URL: {result.get('thumbnail_url')}")
            return True
        else:
            print(f"❌ Logo upload failed: {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Logo upload test failed: {e}")
        return False

def test_single_banner_upload():
    """Test single banner upload endpoint"""
    print("🧪 Testing single banner upload endpoint...")
    
    # Create test user and get token
    token, user_id = create_test_institute_user("banner")
    if not token:
        return False
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # Create test image content
        test_content = create_test_image_data("test_banner.png")
        
        # Upload using single banner endpoint
        files = {"file": ("test_banner.png", base64.b64decode(test_content), "image/png")}
        
        response = client.post(
            "/api/files/institute-banner",
            headers=headers,
            files=files
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Banner upload successful!")
            print(f"   Banner URL: {result.get('banner_url')}")
            print(f"   Thumbnail URL: {result.get('thumbnail_url')}")
            return True
        else:
            print(f"❌ Banner upload failed: {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Banner upload test failed: {e}")
        return False

def test_bulk_upload_with_logo_banner():
    """Test bulk upload with logo and banner"""
    print("🧪 Testing bulk upload with logo and banner...")
    
    # Create test user and get token
    token, user_id = create_test_institute_user("bulk")
    if not token:
        return False
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # Create test image data
        logo_data = ImageData(
            filename="test_logo.png",
            content_type="image/png",
            data=create_test_image_data("test_logo.png")
        )
        
        banner_data = ImageData(
            filename="test_banner.png",
            content_type="image/png",
            data=create_test_image_data("test_banner.png")
        )
        
        # Create bulk upload request
        request_data = InstituteProfileWithDocumentsRequest(
            institute_name="Test University with Logo and Banner",
            description="Testing logo and banner upload",
            city="Test City",
            institute_type="university",
            logo=logo_data,
            banner=banner_data
        ).model_dump()
        
        response = client.put(
            "/api/institutes/profile/with-documents",
            headers=headers,
            json=request_data
        )
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Bulk upload with logo and banner successful!")
            profile = result.get('profile', {})
            print(f"   Institute: {profile.get('institute_name')}")
            print(f"   Logo URL: {profile.get('logo_url')}")
            print(f"   Banner URL: {profile.get('banner_url')}")
            return True
        else:
            print(f"❌ Bulk upload failed: {response.status_code}: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Bulk upload test failed: {e}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    try:
        db = next(get_db())
        
        # Delete test users and their profiles
        test_patterns = ["test_logo_banner_"]
        for pattern in test_patterns:
            users = db.query(User).filter(User.email.like(f"%{pattern}%")).all()
            for user in users:
                # Delete associated profile first
                profile = db.query(InstituteProfile).filter(InstituteProfile.user_id == user.id).first()
                if profile:
                    db.delete(profile)
                db.delete(user)
        
        db.commit()
        print("✅ Cleaned up test data")
        
    except Exception as e:
        print(f"⚠️ Warning: Could not clean up test data: {e}")
    finally:
        db.close()

def main():
    """Main test function"""
    print("🚀 Testing institute logo and banner upload functionality...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 3
    
    # Test 1: Single logo upload
    if test_single_logo_upload():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Test 2: Single banner upload
    if test_single_banner_upload():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Test 3: Bulk upload with logo and banner
    if test_bulk_upload_with_logo_banner():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Cleanup
    cleanup_test_data()
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed! Logo and banner upload is working correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
