import enum

from sqlalchemy import (
    <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
)
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel


class Follower(BaseModel):
    __tablename__ = "followers"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    follower_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    user = relationship("User", foreign_keys=[user_id], back_populates="followers")
    follower = relationship("User", foreign_keys=[follower_id], back_populates="following")

class Follow_Request(BaseModel):
    __tablename__ = "follow_requests"

    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    follower_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)

    user = relationship("User", foreign_keys=[user_id], back_populates="follow_requests_sent")
    follower = relationship("User", foreign_keys=[follower_id], back_populates="follow_requests_received")

