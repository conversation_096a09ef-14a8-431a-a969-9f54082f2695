from typing import List, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone
import bcrypt

# Import Models
from Models.users import InstituteDocument, User, UserTypeEnum, InstituteProfile
from Models.Events import Event, EventStatusEnum

# Import Schemas
from Schemas.Institute.Institute import (
    InstituteRegistrationBase, InstituteProfileUpdate, InstituteVerificationUpdate,
    InstituteUserOut, InstituteDetailedOut, InstituteListOut, InstituteListResponse,
    InstituteSearchFilter, InstituteStatsOut, InstituteVerificationListOut,
    InstituteDocumentOut, InstituteProfileWithDocumentsRequest, InstituteVerificationStatusOut
)


def hash_password(password: str) -> str:
    """Hash password using bcrypt"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')


def register_institute(db: Session, institute_data: InstituteRegistrationBase) -> InstituteUserOut:
    """Register a new institute"""
    
    # Check if username already exists
    existing_user = db.query(User).filter(User.username == institute_data.username).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already exists")
    
    # Check if email already exists
    existing_email = db.query(User).filter(User.email == institute_data.email).first()
    if existing_email:
        raise HTTPException(status_code=400, detail="Email already exists")
    
    # Check if mobile already exists
    existing_mobile = db.query(User).filter(User.mobile == institute_data.mobile).first()
    if existing_mobile:
        raise HTTPException(status_code=400, detail="Mobile number already exists")
    
    # Create user account
    hashed_password = hash_password(institute_data.password)
    
    user = User(
        username=institute_data.username,
        email=institute_data.email,
        mobile=institute_data.mobile,
        password_hash=hashed_password,
        country=institute_data.country,
        user_type=UserTypeEnum.institute,
        is_email_verified=False,
        is_mobile_verified=False
    )
    
    db.add(user)
    db.flush()  # Get the user ID
    
    # Create institute profile
    institute_profile = InstituteProfile(
        user_id=user.id,
        institute_name=institute_data.institute_name,
        description=institute_data.description,
        address=institute_data.address,
        city=institute_data.city,
        state=institute_data.state,
        postal_code=institute_data.postal_code,
        website=institute_data.website,
        established_year=institute_data.established_year,
        institute_type=institute_data.institute_type,
        accreditation=institute_data.accreditation,
        linkedin_url=institute_data.linkedin_url,
        facebook_url=institute_data.facebook_url,
        twitter_url=institute_data.twitter_url,
        is_verified=False,
        verification_status="pending"
    )
    
    db.add(institute_profile)
    db.commit()
    db.refresh(user)
    
    return InstituteUserOut.model_validate(user)



def get_institute_by_id(db: Session, institute_id: uuid.UUID) -> InstituteDetailedOut:
    """Get institute by ID with detailed information"""

    user = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Check if institute profile exists
    if not user.institute_profile:
        raise HTTPException(
            status_code=400,
            detail="Institute profile not found. This indicates a data integrity issue. Please contact support."
        )

    # Get statistics
    total_competitions = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.is_competition == True
    ).count()

    # Active competitions (published and currently ongoing)
    now = datetime.now(timezone.utc)
    active_competitions = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.is_competition == True,
        Event.status == EventStatusEnum.PUBLISHED,
        Event.start_datetime <= now,
        Event.end_datetime > now
    ).count()

    # Get total mentors associated
    from Models.users import MentorInstituteAssociation, InstituteDocument
    total_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "active"
    ).count()

    # Get institute documents
    documents = db.query(InstituteDocument).filter(
        InstituteDocument.institute_id == institute_id
    ).all()

    # Convert documents to proper schema format
    document_list = [InstituteDocumentOut.model_validate(doc) for doc in documents]

    # Create profile data with properly formatted documents
    profile_data = user.institute_profile.__dict__.copy()
    profile_data['documents'] = document_list

    return InstituteDetailedOut(
        user=InstituteUserOut.model_validate(user),
        profile=profile_data,
        total_competitions=total_competitions,
        total_mentors=total_mentors,
        active_competitions=active_competitions,
        verification_status=user.institute_profile.verification_status
    )


def get_institutes(
    db: Session,
    filters: InstituteSearchFilter = None,
    skip: int = 0,
    limit: int = 20
) -> InstituteListResponse:
    """Get institutes with filtering and pagination"""
    
    # Start with base query and explicit join to avoid ambiguity
    query = db.query(User).join(
        InstituteProfile, User.id == InstituteProfile.user_id
    ).options(
        joinedload(User.institute_profile)
    ).filter(User.user_type == UserTypeEnum.institute)

    # Apply filters
    if filters:
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                or_(
                    User.username.ilike(search_term),
                    InstituteProfile.institute_name.ilike(search_term),
                    InstituteProfile.description.ilike(search_term)
                )
            )

        if filters.institute_type:
            query = query.filter(InstituteProfile.institute_type == filters.institute_type.value)

        if filters.country:
            query = query.filter(User.country == filters.country)

        if filters.state:
            query = query.filter(InstituteProfile.state == filters.state)

        if filters.city:
            query = query.filter(InstituteProfile.city == filters.city)

        if filters.verification_status:
            query = query.filter(InstituteProfile.verification_status == filters.verification_status.value)

        if filters.is_verified is not None:
            query = query.filter(InstituteProfile.is_verified == filters.is_verified)

        if filters.established_year_from:
            query = query.filter(InstituteProfile.established_year >= filters.established_year_from)

        if filters.established_year_to:
            query = query.filter(InstituteProfile.established_year <= filters.established_year_to)
    
    # Get total count
    total = query.count()
    
    # Apply pagination and ordering
    institutes = query.order_by(desc(User.created_at)).offset(skip).limit(limit).all()
    
    # Convert to response format
    institute_list = []
    for user in institutes:
        profile = user.institute_profile
        institute_list.append(InstituteListOut(
            id=user.id,
            username=user.username,
            institute_name=profile.institute_name,
            city=profile.city,
            state=profile.state,
            country=user.country,
            institute_type=profile.institute_type,
            is_verified=profile.is_verified,
            verification_status=profile.verification_status,
            profile_picture=profile.logo_url,  # Using logo_url as profile picture
            created_at=user.created_at,
            updated_at=profile.updated_at
        ))
    
    return InstituteListResponse(
        institutes=institute_list,
        total=total,
        page=(skip // limit) + 1,
        size=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0
    )


def update_institute_profile(
    db: Session,
    institute_id: uuid.UUID,
    profile_update: InstituteProfileUpdate
) -> InstituteDetailedOut:
    """Update institute profile (creates if doesn't exist)"""

    user = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="Institute not found")

    profile = user.institute_profile

    # If profile doesn't exist, create it
    if not profile:
        profile = InstituteProfile(
            user_id=institute_id,
            institute_name="",  # Will be updated below
            description="",
            address="",
            city="",
            state="",
            postal_code="",
            website="",
            established_year=None,
            institute_type="",
            accreditation="",
            linkedin_url="",
            facebook_url="",
            twitter_url="",
            logo_url="",
            banner_url="",
            is_verified=False,
            verification_status="pending"
        )
        db.add(profile)
        db.flush()  # Get the profile ID

        # Refresh user to get the new profile relationship
        db.refresh(user)
        profile = user.institute_profile

    # Update profile fields
    update_data = profile_update.model_dump(exclude_unset=True)

    # Handle documents separately
    documents_data = update_data.pop('documents', None)

    for field, value in update_data.items():
        if hasattr(profile, field):
            setattr(profile, field, value)

    # Handle document uploads if provided
    if documents_data:
        from Models.users import InstituteDocument

        # Check maximum 5 documents limit
        existing_documents_count = db.query(InstituteDocument).filter(
            InstituteDocument.institute_id == institute_id
        ).count()

        total_documents_after_update = existing_documents_count + len(documents_data)

        if total_documents_after_update > 5:
            raise HTTPException(
                status_code=400,
                detail=f"Maximum 5 documents allowed per institute. You are trying to add {len(documents_data)} document(s) to {existing_documents_count} existing document(s), which would exceed the limit. Please remove {total_documents_after_update - 5} document(s)."
            )

        for doc_data in documents_data:
            # Create new document record
            document = InstituteDocument(
                institute_id=institute_id,
                document_type=doc_data.get('document_type'),
                document_url=doc_data.get('document_url'),
                document_name=doc_data.get('document_name'),
                description=doc_data.get('description'),
                verified=False
            )
            db.add(document)

    db.commit()
    db.refresh(user)

    return get_institute_by_id(db, institute_id)


def approve_institute(
        db: Session,
        institute_id: uuid.UUID,
        admin_id: uuid.UUID,
        approval_notes: Optional[str] = None
):
    """Approve institute (admin only)"""
    db_institute = (
        db.query(InstituteProfile)
        .join(User, User.id == InstituteProfile.user_id)
        .first()
    )
    if not db_institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    if db_institute.verification_status != "pending":
        raise HTTPException(status_code=400, detail="Institute is not under review")

    db_institute.verification_status = "approved"
    db_institute.verification_notes = approval_notes
    db_institute.is_verified = True
    db_institute.verified_at = datetime.now(timezone.utc)
    db_institute.verified_by = admin_id

    db.commit()
    db.refresh(db_institute)

    return InstituteVerificationStatusOut(
        verification_status=db_institute.verification_status,
        verification_notes=db_institute.verification_notes,
        verified_at=db_institute.verified_at,
    )


def reject_institute(
        db: Session,
        institute_id: uuid.UUID,
        admin_id: uuid.UUID,
        rejection_notes: Optional[str] = None
):
    """Reject institute (admin only)"""
    db_institute = (
        db.query(InstituteProfile)
        .join(User, User.id == InstituteProfile.user_id)
        .first()
    )
    if not db_institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    if db_institute.verification_status != "pending":
        raise HTTPException(status_code=400, detail="Institute is not under review")

    db_institute.verification_status = "rejected"
    db_institute.verification_notes = rejection_notes
    db_institute.is_verified = False
    db_institute.verified_at = datetime.now(timezone.utc)  # could rename to reviewed_at
    db_institute.verified_by = admin_id

    db.commit()
    db.refresh(db_institute)

    return InstituteVerificationStatusOut(
        verification_status=db_institute.verification_status,
        verification_notes=db_institute.verification_notes,
        verified_at=db_institute.verified_at,
    )





def get_institute_verification_status(db: Session, institute_id: uuid.UUID):
    """Get institute verification status (self institute only)"""
    db_institute = db.query(User).filter(User.id == institute_id).join(InstituteProfile, User.id == InstituteProfile.user_id).first()
    if not db_institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    status = InstituteVerificationStatusOut(
        verification_status=db_institute.institute_profile.verification_status,
        verification_notes=db_institute.institute_profile.verification_notes,
        verified_at=db_institute.institute_profile.verified_at
    )
    return status
    
    
def get_institutes_pending_verification(
    db: Session,
    skip: int = 0,
    limit: int = 20
) -> InstituteListResponse:
    """Get institutes pending verification (admin only)"""

    # Get total count first (before applying pagination)
    total_query = db.query(User).join(
        InstituteProfile, User.id == InstituteProfile.user_id
    ).filter(
        User.user_type == UserTypeEnum.institute,
        InstituteProfile.verification_status.in_(["pending", "under_review"])
    )
    total = total_query.count()

    # Get paginated results
    institutes = total_query.options(
        joinedload(User.institute_profile)
    ).order_by(desc(User.created_at)).offset(skip).limit(limit).all()

    # Convert to InstituteListOut format
    institute_list = []
    for user in institutes:
        profile = user.institute_profile

        institute_list.append(InstituteListOut(
            id=user.id,
            username=user.username,
            institute_name=profile.institute_name,
            city=profile.city,
            state=profile.state,
            country=user.country,
            institute_type=profile.institute_type,
            is_verified=profile.is_verified,
            verification_status=profile.verification_status,
            profile_picture=profile.logo_url,
            created_at=user.created_at,
            updated_at=profile.updated_at
        ))

    # Calculate pagination info
    page = (skip // limit) + 1
    has_next = (skip + limit) < total
    has_prev = skip > 0

    return InstituteListResponse(
        institutes=institute_list,
        total=total,
        page=page,
        size=limit,
        has_next=has_next,
        has_prev=has_prev
    )


def get_institute_statistics(db: Session) -> InstituteStatsOut:
    """Get institute statistics (admin only)"""
    
    # Total institutes
    total_institutes = db.query(User).filter(
        User.user_type == UserTypeEnum.institute
    ).count()
    
    # Verified institutes
    verified_institutes = db.query(User).join(InstituteProfile).filter(
        User.user_type == UserTypeEnum.institute,
        InstituteProfile.is_verified == True
    ).count()
    
    # Pending verification
    pending_verification = db.query(User).join(InstituteProfile).filter(
        User.user_type == UserTypeEnum.institute,
        InstituteProfile.verification_status == "pending"
    ).count()
    
    # Rejected institutes
    rejected_institutes = db.query(User).join(InstituteProfile).filter(
        User.user_type == UserTypeEnum.institute,
        InstituteProfile.verification_status == "rejected"
    ).count()
    
    # Institutes by type
    institutes_by_type = {}
    type_counts = db.query(
        InstituteProfile.institute_type,
        func.count(InstituteProfile.id)
    ).join(User).filter(
        User.user_type == UserTypeEnum.institute
    ).group_by(InstituteProfile.institute_type).all()
    
    for institute_type, count in type_counts:
        institutes_by_type[institute_type or "unknown"] = count
    
    # Institutes by country
    institutes_by_country = {}
    country_counts = db.query(
        User.country,
        func.count(User.id)
    ).filter(
        User.user_type == UserTypeEnum.institute
    ).group_by(User.country).all()
    
    for country, count in country_counts:
        institutes_by_country[country or "unknown"] = count
    
    # Recent registrations (last 30 days)
    from datetime import timedelta
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    recent_registrations = db.query(User).filter(
        User.user_type == UserTypeEnum.institute,
        User.created_at >= thirty_days_ago
    ).count()
    
    return InstituteStatsOut(
        total_institutes=total_institutes,
        verified_institutes=verified_institutes,
        pending_verification=pending_verification,
        rejected_institutes=rejected_institutes,
        institutes_by_type=institutes_by_type,
        institutes_by_country=institutes_by_country,
        recent_registrations=recent_registrations
    )


def delete_institute(db: Session, institute_id: uuid.UUID, admin_id: uuid.UUID) -> bool:
    """Delete institute (admin only)"""

    user = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Check if institute has active competitions
    now = datetime.now(timezone.utc)
    active_competitions = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.is_competition == True,
        Event.status == EventStatusEnum.PUBLISHED,
        Event.start_datetime <= now,
        Event.end_datetime > now
    ).count()

    if active_competitions > 0:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete institute with active competitions"
        )

    db.delete(user)
    db.commit()
    return True


async def update_institute_profile_with_documents(
    db: Session,
    institute_id: uuid.UUID,
    request: InstituteProfileWithDocumentsRequest
) -> InstituteDetailedOut:
    """Update institute profile with document uploads (CRUD operation)"""
    from services.file_storage import file_storage
    from Models.users import InstituteDocument
    from Schemas.Institute.Institute import InstituteTypeEnum
    import uuid as uuid_lib
    import base64

    uploaded_files = []

    try:
        # Extract profile data from request (excluding logo and banner which are handled as file uploads)
        profile_data = {
            "institute_name": request.institute_name,
            "description": request.description,
            "address": request.address,
            "city": request.city,
            "state": request.state,
            "postal_code": request.postal_code,
            "website": request.website,
            "established_year": request.established_year,
            "institute_type": request.institute_type,
            "accreditation": request.accreditation,
            "linkedin_url": request.linkedin_url,
            "facebook_url": request.facebook_url,
            "twitter_url": request.twitter_url,
        }

        # Remove None values
        profile_data = {k: v for k, v in profile_data.items() if v is not None}

        # Handle institute_type conversion if provided
        if profile_data.get('institute_type'):
            try:
                profile_data['institute_type'] = InstituteTypeEnum(profile_data['institute_type'])
            except ValueError:
                raise HTTPException(status_code=400, detail=f"Invalid institute type: {profile_data['institute_type']}")

        # Note: We'll create the profile_update object after handling logo/banner uploads
        # so that the logo_url and banner_url are included

        # Get existing documents for this institute
        existing_documents = db.query(InstituteDocument).filter(
            InstituteDocument.institute_id == institute_id
        ).all()

        # Get document names from the request (if any)
        request_document_names = []
        if request.documents:
            request_document_names = [doc.filename for doc in request.documents]

        # Validate maximum 5 documents limit
        if request.documents:
            # Count documents that will remain after deletion
            documents_to_keep = [doc for doc in existing_documents if doc.document_name in request_document_names]
            new_documents = [doc for doc in request.documents if doc.filename not in [existing_doc.document_name for existing_doc in existing_documents]]

            total_documents_after_update = len(documents_to_keep) + len(new_documents)

            if total_documents_after_update > 5:
                raise HTTPException(
                    status_code=400,
                    detail=f"Maximum 5 documents allowed per institute. You are trying to upload {total_documents_after_update} documents. Please remove {total_documents_after_update - 5} document(s)."
                )

        # Delete documents that are not in the request (user removed them)
        documents_to_delete = []
        for existing_doc in existing_documents:
            if existing_doc.document_name not in request_document_names:
                documents_to_delete.append(existing_doc)

        # Delete removed documents from database and file storage
        for doc_to_delete in documents_to_delete:
            try:
                # Delete from file storage
                import os
                full_path = os.path.join("uploads", doc_to_delete.document_url)
                if os.path.exists(full_path):
                    os.remove(full_path)
                    print(f"Deleted file: {full_path}")

                # Delete from database
                db.delete(doc_to_delete)
                print(f"Deleted document from DB: {doc_to_delete.document_name}")

            except Exception as e:
                print(f"Error deleting document {doc_to_delete.document_name}: {str(e)}")
                # Continue with other deletions even if one fails

        # Handle document uploads from base64 data
        if request.documents:
            for doc_data in request.documents:
                try:
                    # Validate base64 data
                    if not doc_data.data or doc_data.data.strip() == "":
                        raise HTTPException(
                            status_code=400,
                            detail=f"Document {doc_data.filename} has empty data. Please provide valid base64 encoded file content."
                        )

                    # Validate document type
                    valid_types = ["accreditation", "license", "certificate", "other"]
                    doc_type = doc_data.document_type or "other"
                    if doc_type.lower() not in valid_types:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid document type: {doc_type}. Must be one of: {', '.join(valid_types)}"
                        )

                    # Check if document with same name already exists (update case)
                    existing_doc = db.query(InstituteDocument).filter(
                        InstituteDocument.institute_id == institute_id,
                        InstituteDocument.document_name == doc_data.filename
                    ).first()

                    if existing_doc:
                        # Update existing document - delete old file first
                        try:
                            import os
                            old_file_path = os.path.join("uploads", existing_doc.document_url)
                            if os.path.exists(old_file_path):
                                os.remove(old_file_path)
                                print(f"Deleted old file: {old_file_path}")
                        except Exception as e:
                            print(f"Warning: Could not delete old file {existing_doc.document_url}: {str(e)}")

                    # Decode base64 data
                    try:
                        file_content = base64.b64decode(doc_data.data)
                        if len(file_content) == 0:
                            raise HTTPException(
                                status_code=400,
                                detail=f"Document {doc_data.filename} decoded to empty content. Please provide valid file data."
                            )
                    except Exception as decode_error:
                        raise HTTPException(
                            status_code=400,
                            detail=f"Invalid base64 data for document {doc_data.filename}: {str(decode_error)}"
                        )

                    # Create a mock UploadFile object from base64 data
                    from io import BytesIO
                    from fastapi import UploadFile

                    file_like = BytesIO(file_content)
                    mock_upload_file = UploadFile(
                        filename=doc_data.filename,
                        file=file_like
                    )

                    # Save file using file storage service
                    file_path = await file_storage.save_institute_document(
                        mock_upload_file, str(institute_id), doc_type
                    )
                    uploaded_files.append(file_path)

                    if existing_doc:
                        # Update existing document record
                        existing_doc.document_type = doc_type.lower()
                        existing_doc.document_url = file_path
                        existing_doc.description = doc_data.description
                        existing_doc.verified = False  # Reset verification on update
                        print(f"Updated existing document: {doc_data.filename}")
                    else:
                        # Create new database record
                        document = InstituteDocument(
                            id=uuid_lib.uuid4(),
                            institute_id=institute_id,
                            document_type=doc_type.lower(),
                            document_url=file_path,
                            document_name=doc_data.filename,
                            description=doc_data.description,
                            verified=False
                        )
                        db.add(document)
                        print(f"Added new document: {doc_data.filename}")

                except HTTPException:
                    # Re-raise HTTP exceptions as-is
                    raise
                except Exception as e:
                    raise HTTPException(status_code=400, detail=f"Error processing document {doc_data.filename}: {str(e)}")

        # Handle logo upload if provided
        if request.logo:
            try:
                # Validate base64 data
                if not request.logo.data or request.logo.data.strip() == "":
                    raise HTTPException(
                        status_code=400,
                        detail="Logo has empty data. Please provide valid base64 encoded image content."
                    )

                # Decode base64 data
                try:
                    file_content = base64.b64decode(request.logo.data)
                    if len(file_content) == 0:
                        raise HTTPException(
                            status_code=400,
                            detail="Logo decoded to empty content. Please provide valid image data."
                        )
                except Exception as decode_error:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid base64 data for logo: {str(decode_error)}"
                    )

                # Create a mock UploadFile object from base64 data
                from io import BytesIO
                from fastapi import UploadFile

                file_like = BytesIO(file_content)
                mock_upload_file = UploadFile(
                    filename=request.logo.filename,
                    file=file_like
                )

                # Save logo using file storage service
                logo_path, logo_thumbnail_path = await file_storage.save_institute_logo(
                    mock_upload_file, str(institute_id)
                )
                uploaded_files.append(logo_path)

                # Update profile data with logo path
                profile_data["logo_url"] = logo_path
                print(f"Uploaded logo: {request.logo.filename}")

            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"Error processing logo {request.logo.filename}: {str(e)}")

        # Handle banner upload if provided
        if request.banner:
            try:
                # Validate base64 data
                if not request.banner.data or request.banner.data.strip() == "":
                    raise HTTPException(
                        status_code=400,
                        detail="Banner has empty data. Please provide valid base64 encoded image content."
                    )

                # Decode base64 data
                try:
                    file_content = base64.b64decode(request.banner.data)
                    if len(file_content) == 0:
                        raise HTTPException(
                            status_code=400,
                            detail="Banner decoded to empty content. Please provide valid image data."
                        )
                except Exception as decode_error:
                    raise HTTPException(
                        status_code=400,
                        detail=f"Invalid base64 data for banner: {str(decode_error)}"
                    )

                # Create a mock UploadFile object from base64 data
                from io import BytesIO
                from fastapi import UploadFile

                file_like = BytesIO(file_content)
                mock_upload_file = UploadFile(
                    filename=request.banner.filename,
                    file=file_like
                )

                # Save banner using file storage service
                banner_path, banner_thumbnail_path = await file_storage.save_institute_banner(
                    mock_upload_file, str(institute_id)
                )
                uploaded_files.append(banner_path)

                # Update profile data with banner path
                profile_data["banner_url"] = banner_path
                print(f"Uploaded banner: {request.banner.filename}")

            except HTTPException:
                raise
            except Exception as e:
                raise HTTPException(status_code=400, detail=f"Error processing banner {request.banner.filename}: {str(e)}")

        # Create profile update object after handling logo/banner uploads
        profile_update = InstituteProfileUpdate(**profile_data)

        # Update profile
        result = update_institute_profile(db, institute_id, profile_update)
        return result

    except Exception as e:
        # Rollback and cleanup on error
        db.rollback()

        # Clean up uploaded files
        for file_path in uploaded_files:
            try:
                import os
                full_path = os.path.join("uploads", file_path)
                if os.path.exists(full_path):
                    os.remove(full_path)
            except Exception:
                pass  # Ignore cleanup errors

        # Re-raise the original exception
        if isinstance(e, HTTPException):
            raise e
        else:
            raise HTTPException(status_code=500, detail=f"An error occurred while updating the profile: {str(e)}")


def get_institute_with_images_by_id(db: Session, institute_id: uuid.UUID):
    """Get institute by ID with image data instead of URLs"""
    from services.file_storage import file_storage
    from Schemas.Institute.Institute import (
        InstituteDetailedWithImagesOut, InstituteUserWithImagesOut,
        InstituteProfileWithImagesOut, ImageResponse
    )

    # Get the regular institute data first
    institute_data = get_institute_by_id(db, institute_id)

    # Convert to dict for manipulation
    if hasattr(institute_data, 'model_dump'):
        institute_dict = institute_data.model_dump()
    elif hasattr(institute_data, 'dict'):
        institute_dict = institute_data.dict()
    else:
        # Convert Pydantic model to dict manually
        institute_dict = {
            'user': institute_data.user.model_dump() if hasattr(institute_data.user, 'model_dump') else institute_data.user.__dict__,
            'profile': institute_data.profile.model_dump() if hasattr(institute_data.profile, 'model_dump') else institute_data.profile.__dict__,
            'total_competitions': institute_data.total_competitions,
            'total_mentors': institute_data.total_mentors,
            'active_competitions': institute_data.active_competitions,
            'verification_status': institute_data.verification_status
        }

    # Convert profile picture URL to image data
    profile_picture_data = None
    if institute_dict['user'].get('profile_picture'):
        profile_pic_data = file_storage.get_file_as_base64(institute_dict['user']['profile_picture'])
        if profile_pic_data:
            profile_picture_data = ImageResponse(**profile_pic_data)

    # Convert logo URL to image data
    logo_data = None
    if institute_dict['profile'].get('logo_url'):
        logo_file_data = file_storage.get_file_as_base64(institute_dict['profile']['logo_url'])
        if logo_file_data:
            logo_data = ImageResponse(**logo_file_data)

    # Convert banner URL to image data
    banner_data = None
    if institute_dict['profile'].get('banner_url'):
        banner_file_data = file_storage.get_file_as_base64(institute_dict['profile']['banner_url'])
        if banner_file_data:
            banner_data = ImageResponse(**banner_file_data)

    # Create the new response with image data
    user_with_images = InstituteUserWithImagesOut(
        id=institute_dict['user']['id'],
        username=institute_dict['user']['username'],
        email=institute_dict['user']['email'],
        mobile=institute_dict['user']['mobile'],
        country=institute_dict['user']['country'],
        profile_picture_data=profile_picture_data,
        user_type=institute_dict['user']['user_type'],
        is_email_verified=institute_dict['user']['is_email_verified'],
        is_mobile_verified=institute_dict['user']['is_mobile_verified'],
        created_at=institute_dict['user']['created_at'],
        institute_profile=None  # Will be set below
    )

    profile_with_images = InstituteProfileWithImagesOut(
        id=institute_dict['profile']['id'],
        user_id=institute_dict['profile']['user_id'],
        institute_name=institute_dict['profile']['institute_name'],
        description=institute_dict['profile']['description'],
        address=institute_dict['profile']['address'],
        city=institute_dict['profile']['city'],
        state=institute_dict['profile']['state'],
        postal_code=institute_dict['profile']['postal_code'],
        website=institute_dict['profile']['website'],
        established_year=institute_dict['profile']['established_year'],
        institute_type=institute_dict['profile']['institute_type'],
        accreditation=institute_dict['profile']['accreditation'],
        is_verified=institute_dict['profile']['is_verified'],
        verification_status=institute_dict['profile']['verification_status'],
        verification_notes=institute_dict['profile']['verification_notes'],
        verified_at=institute_dict['profile']['verified_at'],
        linkedin_url=institute_dict['profile']['linkedin_url'],
        facebook_url=institute_dict['profile']['facebook_url'],
        twitter_url=institute_dict['profile']['twitter_url'],
        logo=logo_data,
        banner=banner_data,
        created_at=institute_dict['profile']['created_at'],
        updated_at=institute_dict['profile']['updated_at'],
        documents=institute_dict['profile']['documents']
    )

    # Set the profile in user
    user_with_images.institute_profile = profile_with_images

    return InstituteDetailedWithImagesOut(
        user=user_with_images,
        profile=profile_with_images,
        total_competitions=institute_dict['total_competitions'],
        total_mentors=institute_dict['total_mentors'],
        active_competitions=institute_dict['active_competitions'],
        verification_status=institute_dict['verification_status']
    )


def get_institute_profile_status(db: Session, institute_id: uuid.UUID):
    """Get institute profile completion status (CRUD operation)"""
    from Schemas.Institute.Institute import InstituteProfileStatus

    user = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Define required fields
    required_fields = [
        "institute_name", "description", "address", "city", "state",
        "established_year", "institute_type"
    ]

    if not user.institute_profile:
        return InstituteProfileStatus(
            profile_exists=False,
            profile_complete=False,
            verification_status="not_created",
            is_verified=False,
            can_submit_for_verification=False,
            required_fields=required_fields,
            missing_fields=required_fields,
            completion_percentage=0.0
        )

    profile = user.institute_profile

    # Check missing fields
    missing_fields = []
    for field in required_fields:
        value = getattr(profile, field, None)
        if not value or (isinstance(value, str) and value.strip() == ""):
            missing_fields.append(field)

    profile_complete = len(missing_fields) == 0
    can_submit = profile_complete and profile.verification_status in ["pending", "rejected"]
    completion_percentage = round((len(required_fields) - len(missing_fields)) / len(required_fields) * 100, 1)

    return InstituteProfileStatus(
        profile_exists=True,
        profile_complete=profile_complete,
        verification_status=profile.verification_status,
        is_verified=profile.is_verified,
        can_submit_for_verification=can_submit,
        required_fields=required_fields,
        missing_fields=missing_fields,
        completion_percentage=completion_percentage,
        verified_at=profile.verified_at,
        verification_notes=profile.verification_notes
    )


