import uuid
from sqlalchemy.orm import Session
from fastapi import HTTPException
from Models.Chapter import Chapter, Topic, SubTopic
from Schemas.TeacherModule.Chapter import ChapterCreate, TopicCreate, SubTopicCreate, ChapterBase, TopicBase, SubTopicBase

def create_chapter(db: Session, chapter: ChapterCreate) -> ChapterBase:
    new_chapter = Chapter(
        name=chapter.name,
        description=chapter.description,
        subject_id=chapter.subject_id
    )
    db.add(new_chapter)
    db.commit()
    db.refresh(new_chapter)
    return new_chapter

def get_all_chapters(db: Session) -> list[ChapterBase]:
    chapters = db.query(Chapter).all()
    return chapters if chapters else []

def get_chapter_by_id(db: Session, chapter_id: str) -> ChapterBase:
    chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
    if not chapter:
        raise HTTPException(status_code=404, detail="Chapter not found.")
    return chapter
def get_chapter_by_subject_id(db: Session, subject_id: str) -> list[ChapterBase]:
    chapters = db.query(Chapter).filter(Chapter.subject_id == subject_id).all()
    return chapters if chapters else []

def update_chapter(db: Session, chapter_id: str, new_chapter: ChapterBase) -> ChapterBase:
    chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
    if not chapter:
        raise HTTPException(status_code=404, detail="Chapter not found.")
    if new_chapter.name is not None:
        chapter.name = new_chapter.name
    if new_chapter.description is not None:
        chapter.description = new_chapter.description
    db.commit()
    db.refresh(chapter)
    return chapter

def delete_chapter(db: Session, chapter_id: str) -> None:
    chapter = db.query(Chapter).filter(Chapter.id == chapter_id).first()
    if not chapter:
        raise HTTPException(status_code=404, detail="Chapter not found.")
    db.delete(chapter)
    db.commit()
    return None

def create_topic(db: Session, topic: TopicCreate) -> TopicBase:
    new_topic = Topic(
        name=topic.name,
        description=topic.description,
        chapter_id=topic.chapter_id
    )
    db.add(new_topic)
    db.commit()
    db.refresh(new_topic)
    return new_topic

def get_all_topics(db: Session) -> list[TopicBase]:
    topics = db.query(Topic).all()
    return topics if topics else []

def get_topic_by_id(db: Session, topic_id: str) -> TopicBase:
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="Topic not found.")
    return topic

def get_topic_by_chapter_id(db: Session, chapter_id: str) -> list[TopicBase]:
    topics = db.query(Topic).filter(Topic.chapter_id == chapter_id).all()
    return topics if topics else []

def update_topic(db: Session, topic_id: str, new_topic: TopicBase) -> TopicBase:
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="Topic not found.")
    if new_topic.name is not None:
        topic.name = new_topic.name
    if new_topic.description is not None:
        topic.description = new_topic.description
    db.commit()
    db.refresh(topic)
    return topic

def delete_topic(db: Session, topic_id: str) -> None:
    topic = db.query(Topic).filter(Topic.id == topic_id).first()
    if not topic:
        raise HTTPException(status_code=404, detail="Topic not found.")
    db.delete(topic)
    db.commit()
    return None

def create_subtopic(db: Session, subtopic: SubTopicCreate) -> SubTopicBase:
    new_subtopic = SubTopic(
        name=subtopic.name,
        description=subtopic.description,
        topic_id=subtopic.topic_id
    )
    db.add(new_subtopic)
    db.commit()
    db.refresh(new_subtopic)
    return new_subtopic

def get_all_subtopics(db: Session) -> list[SubTopicBase]:
    subtopics = db.query(SubTopic).all()
    return subtopics if subtopics else []

def get_subtopic_by_id(db: Session, subtopic_id: str) -> SubTopicBase:
    subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
    if not subtopic:
        raise HTTPException(status_code=404, detail="Subtopic not found.")
    return subtopic

def get_subtopic_by_topic_id(db: Session, topic_id: str) -> list[SubTopicBase]:
    subtopics = db.query(SubTopic).filter(SubTopic.topic_id == topic_id).all()
    return subtopics if subtopics else []

def update_subtopic(db: Session, subtopic_id: str, new_subtopic: SubTopicBase) -> SubTopicBase:
    subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
    if not subtopic:
        raise HTTPException(status_code=404, detail="Subtopic not found.")
    if new_subtopic.name is not None:
        subtopic.name = new_subtopic.name
    if new_subtopic.description is not None:
        subtopic.description = new_subtopic.description
    db.commit()
    db.refresh(subtopic)
    return subtopic

def delete_subtopic(db: Session, subtopic_id: str) -> None:
    subtopic = db.query(SubTopic).filter(SubTopic.id == subtopic_id).first()
    if not subtopic:
        raise HTTPException(status_code=404, detail="Subtopic not found.")
    db.delete(subtopic)
    db.commit()
    return None


