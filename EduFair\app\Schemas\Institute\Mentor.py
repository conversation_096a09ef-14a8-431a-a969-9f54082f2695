from pydantic import BaseModel, <PERSON>, validator, EmailStr
from uuid import UUID
from typing import List, Optional, Dict, Any
from datetime import datetime
from decimal import Decimal
from enum import Enum


class MentorVerificationStatusEnum(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    UNDER_REVIEW = "under_review"


class InviteStatusEnum(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    REJECTED = "rejected"
    EXPIRED = "expired"


class ApplicationStatusEnum(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    REJECTED = "rejected"


class AssociationStatusEnum(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    TERMINATED = "terminated"


# Base Schemas
class MentorRegistrationBase(BaseModel):
    # User account details
    username: str = Field(..., min_length=3, max_length=50, description="Unique username")
    email: EmailStr = Field(..., description="Mentor email address")
    mobile: str = Field(..., description="Mentor contact number")
    password: str = Field(..., min_length=8, description="Account password")
    country: str = Field(..., description="Country")
    
    # Essential mentor profile details
    bio: Optional[str] = Field(None, max_length=2000, description="Professional bio")
    experience_years: Optional[int] = Field(None, ge=0, le=50, description="Years of experience")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Hourly rate in USD")

    # Subject relationships (UUIDs)
    expertise_subject_ids: List[UUID] = Field(default=[], description="Subject IDs for expertise areas")
    preferred_subject_ids: List[UUID] = Field(default=[], description="Subject IDs for preferred subjects")

    # Languages and availability
    languages: Optional[List[str]] = Field(None, description="Languages spoken")
    availability_hours: Optional[Dict[str, Any]] = Field(None, description="Available hours schedule")

    @validator('username')
    def validate_username(cls, v):
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Username can only contain letters, numbers, hyphens, and underscores')
        return v.lower()


class MentorProfileUpdate(BaseModel):
    bio: Optional[str] = Field(None, max_length=2000)
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)

    # Subject relationships (UUIDs)
    expertise_subject_ids: Optional[List[UUID]] = None
    preferred_subject_ids: Optional[List[UUID]] = None

    # Languages and availability
    languages: Optional[List[str]] = None
    availability_hours: Optional[Dict[str, Any]] = None
    profile_image_url: Optional[str] = None


class MentorVerificationUpdate(BaseModel):
    verification_status: MentorVerificationStatusEnum = Field(..., description="New verification status")
    verification_notes: Optional[str] = Field(None, max_length=1000, description="Admin notes")


# Mentor Application Schemas
class MentorApplicationCreate(BaseModel):
    institute_id: UUID = Field(..., description="Institute to apply to")
    application_message: str = Field(..., min_length=10, max_length=1000, description="Application message")
    proposed_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Proposed hourly rate")
    availability_hours: Optional[int] = Field(None, ge=1, le=168, description="Available hours per week")


class MentorApplicationOut(BaseModel):
    id: UUID
    mentor_id: UUID
    institute_id: UUID
    application_message: str
    status: str
    proposed_hourly_rate: Optional[Decimal]
    availability_hours: Optional[int]
    applied_at: datetime
    responded_at: Optional[datetime]
    response_message: Optional[str]
    reviewed_by: Optional[UUID]

    class Config:
        from_attributes = True


# Institute Invitation Schemas
class InstituteInvitationCreate(BaseModel):
    mentor_id: UUID = Field(..., description="UUID of mentor to invite")
    invitation_message: str = Field(..., min_length=10, max_length=1000, description="Invitation message")
    proposed_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Proposed hourly rate")
    proposed_hours_per_week: Optional[int] = Field(None, ge=1, le=168, description="Proposed hours per week")
    expertise_areas_needed: Optional[List[str]] = Field(default=[], description="Required expertise areas")
    contract_terms: Optional[str] = Field(None, max_length=2000, description="Contract terms")


class InstituteInvitationOut(BaseModel):
    id: UUID
    mentor_id: Optional[UUID]
    institute_id: UUID
    mentor_email: str
    invitation_message: str
    status: str
    proposed_hourly_rate: Optional[Decimal]
    proposed_hours_per_week: Optional[int]
    expertise_areas_needed: Optional[List[str]]
    contract_terms: Optional[str]
    invited_at: datetime
    expires_at: Optional[datetime]
    responded_at: Optional[datetime]
    response_message: Optional[str]

    class Config:
        from_attributes = True



# Output Schemas
class MentorProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    bio: Optional[str]
    experience_years: Optional[int]
    hourly_rate: Optional[Decimal]
    languages: Optional[List[str]]
    availability_hours: Optional[Dict[str, Any]]
    profile_image_url: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata

    # Subject relationships (will be populated from relationships)
    expertise_subjects: Optional[List[Dict[str, Any]]] = None
    preferred_subjects: Optional[List[Dict[str, Any]]] = None

    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class MentorUserOut(BaseModel):
    id: UUID
    username: str
    email: str
    mobile: str
    country: str
    profile_picture: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata
    user_type: str
    is_email_verified: bool
    is_mobile_verified: bool
    created_at: datetime
    mentor_profile: Optional[MentorProfileOut]

    class Config:
        from_attributes = True


class MentorDetailedOut(BaseModel):
    user: MentorUserOut
    profile: MentorProfileOut
    total_competitions: int = 0
    active_institutes: int = 0
    average_rating: Optional[Decimal] = None
    verification_status: str

    class Config:
        from_attributes = True


class MentorListOut(BaseModel):
    id: UUID
    username: str
    full_name: str
    email: str
    mobile: str
    country: Optional[str]
    bio: Optional[str]
    expertise_areas: Optional[List[str]]
    experience_years: Optional[int]
    current_position: Optional[str]
    hourly_rate: Optional[Decimal]
    languages: Optional[List[str]]
    rating: Optional[Decimal]
    is_verified: bool
    verification_status: str
    profile_image_url: Optional[str]
    profile_image: Optional[Dict[str, Any]] = None  # Base64 image data with metadata
    created_at: datetime

    class Config:
        from_attributes = True


class MentorListResponse(BaseModel):
    mentors: List[MentorListOut]
    total: int
    page: int
    size: int
    has_next: bool
    has_prev: bool


# Association Output Schemas (Active Relationships Only)
class MentorInstituteAssociationOut(BaseModel):
    id: UUID
    mentor_id: UUID
    institute_id: UUID
    status: str
    hourly_rate: Optional[Decimal]
    hours_per_week: Optional[int]
    contract_terms: Optional[str]
    start_date: datetime
    end_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    created_from_invite_id: Optional[UUID]
    created_from_application_id: Optional[UUID]

    class Config:
        from_attributes = True


class MentorInstituteAssociationCreate(BaseModel):
    mentor_id: UUID = Field(..., description="Mentor ID")
    institute_id: UUID = Field(..., description="Institute ID")
    hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Agreed hourly rate")
    hours_per_week: Optional[int] = Field(None, ge=1, le=168, description="Hours per week")
    contract_terms: Optional[str] = Field(None, max_length=2000, description="Contract terms")
    start_date: Optional[datetime] = Field(None, description="Start date")
    end_date: Optional[datetime] = Field(None, description="End date")
    created_from_invite_id: Optional[UUID] = Field(None, description="Source invite ID")
    created_from_application_id: Optional[UUID] = Field(None, description="Source application ID")


class MentorInstituteAssociationDetailedOut(BaseModel):
    association: MentorInstituteAssociationOut
    mentor: MentorListOut
    institute: Optional[Dict[str, Any]] = None  # Institute details as dict to avoid circular import

    class Config:
        from_attributes = True


# Response Schemas for Invites and Applications
class InviteResponseCreate(BaseModel):
    response_message: Optional[str] = Field(None, max_length=1000, description="Response message")
    accept: bool = Field(..., description="Whether to accept or reject the invite")
    proposed_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Counter-proposed hourly rate")
    proposed_hours_per_week: Optional[int] = Field(None, ge=1, le=168, description="Counter-proposed hours per week")


class ApplicationResponseCreate(BaseModel):
    response_message: Optional[str] = Field(None, max_length=1000, description="Response message")
    accept: bool = Field(..., description="Whether to accept or reject the application")
    approved_hourly_rate: Optional[Decimal] = Field(None, ge=0, description="Approved hourly rate")
    approved_hours_per_week: Optional[int] = Field(None, ge=1, le=168, description="Approved hours per week")
    contract_terms: Optional[str] = Field(None, max_length=2000, description="Contract terms")


# Search and filter schemas
class MentorSearchFilter(BaseModel):
    search: Optional[str] = None
    country: Optional[str] = None
    is_verified: Optional[bool] = None


class AssociationSearchFilter(BaseModel):
    status: Optional[AssociationStatusEnum] = None
    institute_id: Optional[UUID] = None
    mentor_id: Optional[UUID] = None


class InviteSearchFilter(BaseModel):
    status: Optional[InviteStatusEnum] = None
    institute_id: Optional[UUID] = None
    mentor_email: Optional[str] = None


class ApplicationSearchFilter(BaseModel):
    status: Optional[ApplicationStatusEnum] = None
    institute_id: Optional[UUID] = None
    mentor_id: Optional[UUID] = None


# Statistics schemas
class MentorStatsOut(BaseModel):
    total_mentors: int
    verified_mentors: int
    pending_verification: int
    active_associations: int
    mentors_by_expertise: Dict[str, int]
    average_hourly_rate: Optional[Decimal]
    recent_registrations: int  # Last 30 days


class InstituteAssociationStatsOut(BaseModel):
    total_associations: int
    pending_applications: int
    active_mentors: int
    average_mentor_rating: Optional[Decimal]
    total_competitions_checked: int
