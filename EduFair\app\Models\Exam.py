from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Table, Boolean, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
from datetime import datetime, timezone
import uuid

# Association table for Exam <-> Question many-to-many
exam_question_association = Table(
    'exam_question_association', BaseModel.metadata,
    Column('exam_id', UUID(as_uuid=True), ForeignKey('exams.id'), primary_key=True),
    Column('question_id', UUID(as_uuid=True), ForeignKey('questions.id'), primary_key=True)
)

class Exam(BaseModel):
    __tablename__ = 'exams'
    title = Column(String, nullable=False)
    description = Column(String, nullable=True)
    total_marks = Column(Integer, nullable=False, default=0)
    total_duration = Column(Integer, nullable=False, default=0)  # Duration in minutes
    start_time = Column(DateTime, nullable=True)  # When the exam starts
    # End time is calculated as start_time + total_duration

    questions = relationship(
        'Question',
        secondary=exam_question_association,
        backref='exams',
        lazy='joined'
    )
    student_assignments = relationship(
        'StudentExamAssignment',
        back_populates='exam',
        cascade='all, delete-orphan'
    )

class StudentExamAssignment(BaseModel):
    __tablename__ = 'student_exam_assignments'
    exam_id = Column(UUID(as_uuid=True), ForeignKey('exams.id', ondelete="CASCADE"), primary_key=True)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), primary_key=True)
    assigned_at = Column(DateTime, default=datetime.utcnow)
    status = Column(String, default='assigned')  # assigned, started, completed
    started_at = Column(DateTime, nullable=True)
    completed_at = Column(DateTime, nullable=True)
    assignment_source = Column(String, nullable=True)  # 'individual', 'classroom', 'mixed'
    source_classroom_id = Column(UUID(as_uuid=True), ForeignKey('classrooms.id'), nullable=True)
    # Relationships
    exam = relationship('Exam', back_populates='student_assignments')
    student = relationship('User', backref='exam_assignments')
    source_classroom = relationship('Classroom', backref='exam_assignments')

class StudentExamAttempt(BaseModel):
    __tablename__ = 'student_exam_attempts'
    id = Column(UUID(as_uuid=True), primary_key=True)
    exam_id = Column(UUID(as_uuid=True), ForeignKey('exams.id', ondelete="CASCADE"), nullable=False)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    started_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Integer, nullable=True)  # actual time taken
    status = Column(String, nullable=True)  # submitted, disqualified, etc.
    is_ai_checked = Column(Boolean, default=False)
    is_teacher_checked = Column(Boolean, default=False)
    # Relationships
    exam = relationship('Exam', backref='student_attempts')
    student = relationship('User', backref='exam_attempts')
    answers = relationship('StudentExamAnswer', back_populates='attempt', cascade='all, delete-orphan')
    question_order = relationship('StudentExamQuestionOrder', back_populates='attempt', cascade='all, delete-orphan')
    ai_results = relationship('StudentExamAIResult', back_populates='attempt', cascade='all, delete-orphan')
    teacher_results = relationship('StudentExamTeacherResult', back_populates='attempt', cascade='all, delete-orphan')

class StudentExamAnswer(BaseModel):
    __tablename__ = 'student_exam_answers'
    attempt_id = Column(UUID(as_uuid=True), ForeignKey('student_exam_attempts.id', ondelete="CASCADE"), primary_key=True)
    question_id = Column(UUID(as_uuid=True), ForeignKey('questions.id'), primary_key=True)
    answer = Column(String, nullable=True)  # Student's answer text
    answer_text = Column(String, nullable=True)  # Alias for compatibility
    submitted_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))  # When answer was submitted
    time_spent_seconds = Column(Integer, nullable=True)  # Time spent on this question
    # Relationships
    attempt = relationship('StudentExamAttempt', back_populates='answers')
    question = relationship('Question')

class StudentExamQuestionOrder(BaseModel):
    __tablename__ = 'student_exam_question_order'
    attempt_id = Column(UUID(as_uuid=True), ForeignKey('student_exam_attempts.id', ondelete="CASCADE"), primary_key=True)
    order_index = Column(Integer, primary_key=True)
    question_id = Column(UUID(as_uuid=True), ForeignKey('questions.id'), nullable=False)
    # Relationships
    attempt = relationship('StudentExamAttempt', back_populates='question_order')
    question = relationship('Question')

class StudentExamAIResult(BaseModel):
    __tablename__ = 'student_exam_ai_results'
    attempt_id = Column(UUID(as_uuid=True), ForeignKey('student_exam_attempts.id', ondelete="CASCADE"), primary_key=True)
    question_id = Column(UUID(as_uuid=True), ForeignKey('questions.id'), primary_key=True)
    ai_score = Column(Integer, nullable=True)
    ai_feedback = Column(String, nullable=True)
    # Relationships
    attempt = relationship('StudentExamAttempt', back_populates='ai_results')
    question = relationship('Question')

class StudentExamTeacherResult(BaseModel):
    __tablename__ = 'student_exam_teacher_results'
    attempt_id = Column(UUID(as_uuid=True), ForeignKey('student_exam_attempts.id', ondelete="CASCADE"), primary_key=True)
    question_id = Column(UUID(as_uuid=True), ForeignKey('questions.id'), primary_key=True)
    teacher_score = Column(Integer, nullable=True)
    teacher_feedback = Column(String, nullable=True)
    # Relationships
    attempt = relationship('StudentExamAttempt', back_populates='teacher_results')
    question = relationship('Question')

class StudentExamCheatIncident(BaseModel):
    __tablename__ = 'student_exam_cheat_incidents'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    attempt_id = Column(UUID(as_uuid=True), ForeignKey('student_exam_attempts.id', ondelete="CASCADE"), nullable=False)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    detected_at = Column(DateTime, default=datetime.utcnow)
    cheat_type = Column(String, nullable=False)
    proof = Column(String, nullable=False)  # Could be a URL, text, or file path
    status = Column(String, default='notified')  # notified, under_review, cleared, etc.

    attempt = relationship('StudentExamAttempt', backref='cheat_incidents')
    student = relationship('User') 