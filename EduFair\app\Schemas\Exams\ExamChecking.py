from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from uuid import UUID
from datetime import datetime
from enum import Enum

class ExamCheckingStatusEnum(str, Enum):
    pending = "pending"
    ai_checking = "ai_checking"
    ai_checked = "ai_checked"
    teacher_checking = "teacher_checking"
    checked = "checked"
    rejected = "rejected"

class ExamCheckingBase(BaseModel):
    exam_id: UUID
    student_id: UUID
    status: ExamCheckingStatusEnum = ExamCheckingStatusEnum.pending
    score: Optional[int] = None
    feedback: Optional[str] = None
    checked_at: Optional[datetime] = None
    checked_by: Optional[UUID] = None

class ExamCheckingCreate(ExamCheckingBase):
    pass

class ExamCheckingUpdate(BaseModel):
    status: Optional[ExamCheckingStatusEnum] = None
    score: Optional[int] = None
    feedback: Optional[str] = None
    checked_at: Optional[datetime] = None
    checked_by: Optional[UUID] = None

class ExamCheckingOut(ExamCheckingBase):
    id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

# AI Checking Schemas
class QuestionFeedback(BaseModel):
    """Feedback for a single question"""
    question_id: UUID
    question_text: str
    correct_answer: Optional[str] = None
    student_answer: str
    student_attempted_answer: str = Field(..., description="Student's actual attempted answer")  # ✅ ADDED
    ai_score: int = Field(..., ge=0, description="AI assigned score for this question")
    max_score: int = Field(..., gt=0, description="Maximum possible score for this question")
    feedback: str = Field(..., description="AI feedback explaining the score")
    suggestions: Optional[str] = Field(None, description="Suggestions for improvement")

class ExamAIResult(BaseModel):
    """Complete AI checking result for an exam attempt"""
    attempt_id: UUID
    exam_title: str
    student_name: str
    is_disqualified: bool = Field(default=False, description="Whether the student was disqualified from the exam")
    disqualification_reason: Optional[str] = Field(None, description="Reason for disqualification if applicable")
    attempt_status: Optional[str] = Field(None, description="Status of the exam attempt (submitted, disqualified, etc.)")
    total_score: int = Field(..., ge=0, description="Total score achieved")
    max_total_score: int = Field(..., gt=0, description="Maximum possible total score")
    percentage: float = Field(..., ge=0, le=100, description="Percentage score")
    overall_feedback: str = Field(..., description="Overall exam performance feedback")
    strengths: List[str] = Field(default_factory=list, description="Areas where student performed well")
    improvements: List[str] = Field(default_factory=list, description="Areas needing improvement")
    question_feedbacks: List[QuestionFeedback] = Field(..., description="Detailed feedback for each question")
    ai_checked_at: datetime = Field(default_factory=lambda: datetime.now(), description="When AI checking was completed")

class AICheckingRequest(BaseModel):
    """Request to start AI checking for an exam attempt"""
    attempt_id: UUID

class AICheckingResponse(BaseModel):
    """Response from AI checking request"""
    success: bool
    message: str
    is_disqualified: bool = Field(default=False, description="Whether the student was disqualified")
    disqualification_reason: Optional[str] = Field(None, description="Reason for disqualification if applicable")
    result: Optional[ExamAIResult] = None
    error_details: Optional[str] = None

# Basic Exam Results Schemas (without AI processing)
class BasicExamResult(BaseModel):
    """Basic exam result showing scores from teacher or AI checking"""
    exam_id: str
    student_id: str
    attempt_id: str
    exam_title: str
    student_name: str
    is_disqualified: bool = Field(default=False, description="Whether the student was disqualified from the exam")
    disqualification_reason: Optional[str] = Field(None, description="Reason for disqualification if applicable")
    attempt_status: str = Field(description="Status of the exam attempt (submitted, disqualified, etc.)")
    total_score: float = Field(description="Total score achieved by the student")
    max_total_score: float = Field(description="Maximum possible score for the exam")
    percentage: float = Field(description="Percentage score (total_score/max_total_score * 100)")
    has_teacher_results: bool = Field(description="Whether teacher has graded this exam")
    has_ai_results: bool = Field(description="Whether AI has checked this exam")
    results_source: str = Field(description="Source of results: 'teacher', 'ai', or 'none'")
    submitted_at: Optional[str] = Field(None, description="When the exam was submitted (ISO format)")


class BasicExamResultsResponse(BaseModel):
    """Response for basic exam results endpoint"""
    success: bool
    message: Optional[str] = None
    is_disqualified: bool = Field(default=False, description="Whether the student was disqualified from the exam")
    disqualification_reason: Optional[str] = Field(None, description="Reason for disqualification if applicable")
    result: Optional[BasicExamResult] = None