import json
import uuid
from datetime import datetime, timedelta, timezone
from typing import List, Optional, Dict
from sqlalchemy.orm import Session
from fastapi import HTTPException, Depends
from Models.Exam import Exam, StudentExamAttempt, StudentExamAnswer
from Models.users import User
from Models.MongoModels import AdminActionLog, ReconnectionRequestLog, ExamSessionLog
from Schemas.Exams.ExamSession import (
    StartSessionRequest, SubmitSessionRequest, ReconnectionRequest,
    TeacherApprovalRequest, SessionResumeResponse
)
from config.mongodb import get_mongodb

class ExamSessionCRUD:
    def __init__(self, redis_client, mongo_db=None):
        self.redis = redis_client
        self.mongo_db = mongo_db

    async def start_session(self, db: Session, student_id: str, exam_id: str, mongo_db=None) -> str:
        """Start a new exam session"""
        # Validate exam exists
        exam = db.query(Exam).filter(Exam.id == exam_id).first()
        if not exam:
            raise HTTPException(status_code=404, detail="Exam not found.")
        
        session_id = str(uuid.uuid4())
        session_key = f"exam_session:{session_id}"
        now = datetime.now(timezone.utc)
        
        session_data = {
            "student_id": student_id,
            "exam_id": exam_id,
            "answers": json.dumps({}),
            "strikes": 0,
            "last_heartbeat": now.isoformat(),
            "status": "active",
            "start_time": now.isoformat(),
            "duration": str(exam.total_duration * 60)  # seconds
        }
        
        await self.redis.hset(session_key, mapping=session_data)
        await self.redis.expire(session_key, exam.total_duration * 60 + 300)  # 5 min grace
        
        # Log to MongoDB
        if mongo_db is not None:
            session_log = ExamSessionLog(
                session_id=session_id,
                student_id=str(student_id),  # Convert to string
                exam_id=str(exam_id),  # Convert to string
                action="started",
                details={"exam_title": str(exam.title)}
            )
            await mongo_db.exam_sessions.insert_one(session_log.model_dump(by_alias=True))
        
        return session_id

    async def submit_session(self, db: Session, session_id: str, student_id: str, exam_data: dict = None, questions_data: list = None, student_answers: list = None, mongo_db=None) -> Dict:
        """Submit an exam session with enhanced validation"""
        session_key = f"exam_session:{session_id}"
        session = await self.redis.hgetall(session_key)

        if not session:
            raise HTTPException(status_code=404, detail="Session not found or expired.")

        if session.get("student_id") != student_id:
            raise HTTPException(status_code=403, detail="You do not have permission to submit this session.")

        # Validate required data is provided
        if not exam_data:
            raise HTTPException(status_code=400, detail="Exam object is required for submission.")

        if not questions_data or len(questions_data) == 0:
            raise HTTPException(status_code=400, detail="Questions data is required and cannot be empty.")

        # Check if student is disqualified - allow empty answers for disqualified students
        status_val = session.get("status", "ended")
        is_disqualified = status_val == "disqualified"

        if not is_disqualified and (not student_answers or len(student_answers) == 0):
            raise HTTPException(status_code=400, detail="Student answers are required for normal submissions.")

        # For disqualified students, ensure we have empty answers list if none provided
        if is_disqualified and not student_answers:
            student_answers = []

        # Validate exam_id matches session
        if exam_data.get('exam_id') != session.get('exam_id'):
            raise HTTPException(status_code=400, detail="Exam ID mismatch between session and submission data.")

        # Save to PostgreSQL
        answers = json.loads(session.get("answers", "{}"))
        strikes = int(session.get("strikes", 0))
        status_val = session.get("status", "ended")
        disqualification_reason = None

        if status_val == "disqualified":
            disqualification_reason = f"Cheat strikes: {strikes}"
        
        # Ensure UUIDs are properly converted
        exam_id = session["exam_id"]
        student_id = session["student_id"]

        # Convert to UUID if they're strings
        if isinstance(exam_id, str):
            exam_id = uuid.UUID(exam_id)
        if isinstance(student_id, str):
            student_id = uuid.UUID(student_id)

        attempt = StudentExamAttempt(
            id=uuid.uuid4(),
            exam_id=exam_id,
            student_id=student_id,
            started_at=datetime.fromisoformat(session["start_time"]),
            completed_at=datetime.now(timezone.utc),
            is_teacher_checked=False,
            is_ai_checked=False,
        )
        
        if disqualification_reason:
            attempt.status = "disqualified"
        
        db.add(attempt)
        db.commit()
        db.refresh(attempt)
        
        # Save answers with comprehensive data
        current_time = datetime.now(timezone.utc)

        # Process student answers - handle empty answers for disqualified students
        if student_answers and len(student_answers) > 0:
            for answer_data in student_answers:
                question_id = uuid.UUID(answer_data.get('question_id')) if isinstance(answer_data.get('question_id'), str) else answer_data.get('question_id')

                student_answer = StudentExamAnswer(
                    attempt_id=attempt.id,
                    question_id=question_id,
                    answer=answer_data.get('answer', ''),
                    answer_text=answer_data.get('answer', ''),
                    submitted_at=current_time,
                    time_spent_seconds=answer_data.get('time_spent_seconds')
                )
                db.add(student_answer)
        elif is_disqualified:
            # For disqualified students with no answers, create empty answer records for each question
            for question_data in questions_data:
                question_id = uuid.UUID(question_data.get('question_id')) if isinstance(question_data.get('question_id'), str) else question_data.get('question_id')

                empty_answer = StudentExamAnswer(
                    attempt_id=attempt.id,
                    question_id=question_id,
                    answer='',
                    answer_text='',
                    submitted_at=current_time,
                    time_spent_seconds=0
                )
                db.add(empty_answer)

        db.commit()
        
        # Log to MongoDB with enhanced details
        if mongo_db is not None:
            session_log = ExamSessionLog(
                session_id=session_id,
                student_id=str(student_id),  # Convert UUID to string
                exam_id=str(session["exam_id"]),  # Convert UUID to string
                action="submitted",
                details={
                    "strikes": strikes,
                    "disqualified": status_val == "disqualified",
                    "disqualification_reason": disqualification_reason or "",
                    "exam_title": exam_data.get('title', ''),
                    "total_questions": len(questions_data),
                    "total_answers": len(student_answers),
                    "total_marks": exam_data.get('total_marks', 0),
                    "exam_duration": exam_data.get('total_duration', 0)
                }
            )
            await mongo_db.exam_sessions.insert_one(session_log.model_dump(by_alias=True))
        
        await self.redis.delete(session_key)
        
        return {
            "success": True,
            "disqualified": status_val == "disqualified",
            "disqualification_reason": disqualification_reason,
            "attempt_id": str(attempt.id),
            "status": attempt.status or "submitted"
        }

    async def request_reconnection(self, db: Session, session_id: str, student_id: str, reason: str, mongo_db=None) -> str:
        """Request reconnection to an exam session"""
        session_key = f"exam_session:{session_id}"
        session = await self.redis.hgetall(session_key)
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found.")
        
        if session.get("student_id") != student_id:
            raise HTTPException(status_code=403, detail="You can only request reconnection for your own session.")
        
        # Check if session is still within time limits
        start_time = session.get("start_time")
        duration = int(session.get("duration", "0"))
        if start_time and duration:
            start_dt = datetime.fromisoformat(start_time)
            end_dt = start_dt + timedelta(seconds=duration)
            if datetime.now(timezone.utc) >= end_dt:
                raise HTTPException(status_code=400, detail="Exam time has expired.")
        
        # Create reconnection request
        request_id = str(uuid.uuid4())
        request_key = f"reconnection_request:{request_id}"
        request_data = {
            "session_id": session_id,
            "student_id": student_id,
            "exam_id": session["exam_id"],
            "reason": reason,
            "status": "pending_approval",
            "requested_at": datetime.now(timezone.utc).isoformat()
        }
        
        await self.redis.hset(request_key, mapping=request_data)
        await self.redis.expire(request_key, 60 * 60 * 24)  # 24 hours expiry
        
        # Mark session as pending reconnection
        await self.redis.hset(session_key, "status", "pending_reconnection")
        
        # Log to MongoDB
        if mongo_db is not None:
            reconnection_log = ReconnectionRequestLog(
                request_id=request_id,
                session_id=session_id,
                student_id=student_id,
                exam_id=session["exam_id"],
                reason=reason,
                status="pending_approval"
            )
            await mongo_db.reconnection_requests.insert_one(reconnection_log.model_dump(by_alias=True))
        
        return request_id

    async def get_pending_reconnection_requests(self, db: Session, teacher_id: str) -> List[Dict]:
        """Get pending reconnection requests for a teacher's exams"""
        keys = await self.redis.keys("reconnection_request:*")
        requests = []
        
        for key in keys:
            request = await self.redis.hgetall(key)
            if request.get("status") == "pending_approval":
                # Check if this teacher owns the exam
                exam = db.query(Exam).filter(Exam.id == request["exam_id"]).first()
                if exam and any(q.teacher_id == teacher_id for q in exam.questions):
                    requests.append({
                        "request_id": key.split(":", 1)[-1],
                        **request
                    })
        
        return requests

    async def approve_reconnection_request(self, db: Session, request_id: str, teacher_id: str, approved: bool, reason: str = None, mongo_db=None) -> Dict:
        """Approve or deny a reconnection request"""
        request_key = f"reconnection_request:{request_id}"
        request = await self.redis.hgetall(request_key)
        
        if not request:
            raise HTTPException(status_code=404, detail="Reconnection request not found.")
        
        # Verify teacher owns the exam
        exam = db.query(Exam).filter(Exam.id == request["exam_id"]).first()
        if not exam or not any(q.teacher_id == teacher_id for q in exam.questions):
            raise HTTPException(status_code=403, detail="You can only approve requests for your own exams.")
        
        session_key = f"exam_session:{request['session_id']}"
        session = await self.redis.hgetall(session_key)
        if not session:
            raise HTTPException(status_code=404, detail="Original session not found.")
        
        if approved:
            # Approve reconnection
            await self.redis.hset(request_key, mapping={
                "status": "approved",
                "approved_by": teacher_id,
                "approved_at": datetime.now(timezone.utc).isoformat(),
                "teacher_reason": reason or "Approved"
            })
            
            # Reactivate session
            await self.redis.hset(session_key, mapping={
                "status": "active",
                "last_heartbeat": datetime.now(timezone.utc).isoformat()
            })
            
            # Update MongoDB log
            if mongo_db is not None:
                await mongo_db.reconnection_requests.update_one(
                    {"request_id": request_id},
                    {
                        "$set": {
                            "status": "approved",
                            "approved_by": teacher_id,
                            "approved_at": datetime.now(timezone.utc),
                            "teacher_reason": reason or "Approved"
                        }
                    }
                )
            
            return {"status": "approved", "session_id": request["session_id"]}
        else:
            # Deny reconnection
            await self.redis.hset(request_key, mapping={
                "status": "denied",
                "denied_by": teacher_id,
                "denied_at": datetime.now(timezone.utc).isoformat(),
                "teacher_reason": reason or "Denied"
            })
            
            # Update MongoDB log
            if mongo_db is not None:
                await mongo_db.reconnection_requests.update_one(
                    {"request_id": request_id},
                    {
                        "$set": {
                            "status": "denied",
                            "denied_by": teacher_id,
                            "denied_at": datetime.now(timezone.utc),
                            "teacher_reason": reason or "Denied"
                        }
                    }
                )
            
            return {"status": "denied"}

    async def get_session_resume_data(self, db: Session, session_id: str, student_id: str) -> SessionResumeResponse:
        """Get session data for resuming an exam"""
        session_key = f"exam_session:{session_id}"
        session = await self.redis.hgetall(session_key)
        
        if not session:
            raise HTTPException(status_code=404, detail="Session not found.")
        
        if session.get("student_id") != student_id:
            raise HTTPException(status_code=403, detail="You can only resume your own session.")
        
        if session.get("status") != "active":
            raise HTTPException(status_code=400, detail="Session is not active.")
        
        # Get exam details
        exam = db.query(Exam).filter(Exam.id == session["exam_id"]).first()
        if not exam:
            raise HTTPException(status_code=404, detail="Exam not found.")
        
        # Calculate remaining time
        start_time = datetime.fromisoformat(session["start_time"])
        duration_seconds = int(session["duration"])
        end_time = start_time + timedelta(seconds=duration_seconds)
        remaining_seconds = max(0, int((end_time - datetime.now(timezone.utc)).total_seconds()))
        
        return SessionResumeResponse(
            session_id=session_id,
            exam_id=session["exam_id"],
            exam_title=exam.title,
            current_answers=json.loads(session.get("answers", "{}")),
            remaining_time_seconds=remaining_seconds,
            total_duration_seconds=duration_seconds,
            strikes=int(session.get("strikes", 0)),
            status=session.get("status")
        )

    async def log_admin_action(self, mongo_db, admin_id: str, action: str, session_id: str, reason: str = None, details: dict = None):
        """Log admin actions to MongoDB"""
        if mongo_db is not None:
            admin_log = AdminActionLog(
                admin_id=admin_id,
                action=action,
                session_id=session_id,
                reason=reason or "",
                details=details or {}
            )
            await mongo_db.admin_actions.insert_one(admin_log.model_dump(by_alias=True))

    async def get_active_sessions(self) -> List[Dict]:
        """Get all active exam sessions"""
        keys = await self.redis.keys("exam_session:*")
        sessions = []
        
        for key in keys:
            session = await self.redis.hgetall(key)
            if session.get("status") == "active":
                sessions.append({
                    "session_id": key.split(":", 1)[-1],
                    **session
                })
        
        return sessions 