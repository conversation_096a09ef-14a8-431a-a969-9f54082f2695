from typing import List, Dict, Any, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, extract
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
from decimal import Decimal

# Import Models
from Models.users import User, UserTypeEnum
from Models.Events import Event, EventRegistration, EventStatusEnum, EventCategory

# Import Schemas
from Schemas.Institute.EventManagement import (
    InstituteEventCreate, InstituteEventOut, EventAttendeeOut, EventAttendeesResponse,
    EventRegistrationRequest, EventCheckInRequest, EventReminderRequest,
    EventCategoryCreate, EventCategoryOut, EventTemplateCreate, EventTemplateOut,
    EventAnalyticsOut, EventAttendanceReportOut, EventSuccessMetricsOut,
    EventFeedbackSurveyCreate, EventFeedbackResultsOut, InstituteEventsResponse,
    EventPublishRequest, EventCancelRequest
)


def create_institute_event(
    db: Session,
    institute_id: uuid.UUID,
    event_data: InstituteEventCreate
) -> InstituteEventOut:
    """Create event for institute"""
    
    # Verify institute exists
    institute = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    
    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    # Create event
    event = Event(
        id=uuid.uuid4(),
        title=event_data.title,
        description=event_data.description,
        short_description=event_data.description[:200] + "..." if len(event_data.description) > 200 else event_data.description,
        start_datetime=event_data.start_datetime,
        end_datetime=event_data.end_datetime,
        organizer_id=institute_id,
        institute_id=institute_id,
        category_id=event_data.category_id,
        status=EventStatusEnum.draft,
        is_public=event_data.is_public,
        registration_required=event_data.registration_required,
        registration_start=datetime.now(timezone.utc),
        registration_end=event_data.registration_deadline,
        max_attendees=event_data.max_attendees,
        banner_image_url=event_data.banner_image_url,
        created_at=datetime.now(timezone.utc),
        updated_at=datetime.now(timezone.utc)
    )
    
    db.add(event)
    db.commit()
    db.refresh(event)
    
    return convert_event_to_institute_out(event)


def get_institute_events(
    db: Session,
    institute_id: uuid.UUID,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 20
) -> InstituteEventsResponse:
    """Get events for institute"""
    
    # Build query
    query = db.query(Event).filter(Event.institute_id == institute_id)
    
    if status:
        query = query.filter(Event.status == status)
    
    # Get total count
    total = query.count()
    
    # Get events with pagination
    events = query.order_by(desc(Event.created_at)).offset(skip).limit(limit).all()
    
    # Convert to response format
    event_list = [convert_event_to_institute_out(event) for event in events]
    
    # Get status counts
    upcoming = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.start_datetime > datetime.now(timezone.utc),
        Event.status == EventStatusEnum.PUBLISHED
    ).count()

    ongoing = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.status == EventStatusEnum.PUBLISHED,
        Event.start_datetime <= datetime.now(timezone.utc),
        Event.end_datetime > datetime.now(timezone.utc)
    ).count()

    completed = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.status == EventStatusEnum.COMPLETED
    ).count()

    cancelled = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.status == EventStatusEnum.CANCELLED
    ).count()
    
    return InstituteEventsResponse(
        events=event_list,
        total=total,
        upcoming=upcoming,
        ongoing=ongoing,
        completed=completed,
        cancelled=cancelled
    )


def get_institute_event(
    db: Session,
    institute_id: uuid.UUID,
    event_id: uuid.UUID
) -> InstituteEventOut:
    """Get specific event for institute"""
    
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == institute_id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    return convert_event_to_institute_out(event)


def update_institute_event(
    db: Session,
    institute_id: uuid.UUID,
    event_id: uuid.UUID,
    event_data: InstituteEventCreate
) -> InstituteEventOut:
    """Update event for institute"""
    
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == institute_id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Update event fields
    event.title = event_data.title
    event.description = event_data.description
    event.short_description = event_data.description[:200] + "..." if len(event_data.description) > 200 else event_data.description
    event.start_datetime = event_data.start_datetime
    event.end_datetime = event_data.end_datetime
    event.category_id = event_data.category_id
    event.is_public = event_data.is_public
    event.registration_required = event_data.registration_required
    event.registration_end = event_data.registration_deadline
    event.max_attendees = event_data.max_attendees
    event.banner_image_url = event_data.banner_image_url
    event.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(event)
    
    return convert_event_to_institute_out(event)


def delete_institute_event(
    db: Session,
    institute_id: uuid.UUID,
    event_id: uuid.UUID
) -> bool:
    """Delete event for institute"""
    
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == institute_id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Check if event has registrations
    from Models.Events import RegistrationStatusEnum
    registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.status == RegistrationStatusEnum.CONFIRMED
    ).count()
    
    if registrations > 0:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete event with confirmed registrations. Cancel the event instead."
        )
    
    db.delete(event)
    db.commit()
    return True


def publish_event(
    db: Session,
    institute_id: uuid.UUID,
    event_id: uuid.UUID,
    publish_data: EventPublishRequest
) -> InstituteEventOut:
    """Publish event"""
    
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == institute_id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    if event.status != EventStatusEnum.DRAFT:
        raise HTTPException(status_code=400, detail="Only draft events can be published")

    # Update event status
    event.status = EventStatusEnum.PUBLISHED
    event.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(event)
    
    # TODO: Send notifications to followers if requested
    
    return convert_event_to_institute_out(event)


def cancel_event(
    db: Session,
    institute_id: uuid.UUID,
    event_id: uuid.UUID,
    cancel_data: EventCancelRequest
) -> InstituteEventOut:
    """Cancel event"""
    
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == institute_id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    if event.status == EventStatusEnum.CANCELLED:
        raise HTTPException(status_code=400, detail="Event is already cancelled")

    # Update event status
    event.status = EventStatusEnum.CANCELLED
    event.updated_at = datetime.now(timezone.utc)
    
    db.commit()
    db.refresh(event)
    
    # TODO: Send cancellation notifications to attendees
    # TODO: Process refunds based on refund_policy
    
    return convert_event_to_institute_out(event)


def get_event_attendees(
    db: Session,
    institute_id: uuid.UUID,
    event_id: uuid.UUID,
    status: Optional[str] = None
) -> EventAttendeesResponse:
    """Get attendees for event"""
    
    # Verify event belongs to institute
    event = db.query(Event).filter(
        Event.id == event_id,
        Event.institute_id == institute_id
    ).first()
    
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")
    
    # Build query
    query = db.query(EventRegistration).options(
        joinedload(EventRegistration.user)
    ).filter(EventRegistration.event_id == event_id)
    
    if status:
        query = query.filter(EventRegistration.status == status)
    
    registrations = query.all()
    
    # Convert to attendee format
    attendees = []
    for reg in registrations:
        user = reg.user
        attendees.append(EventAttendeeOut(
            id=reg.id,
            user_id=reg.user_id,
            first_name=user.first_name,
            last_name=user.last_name,
            email=user.email,
            phone=user.mobile,
            registration_date=reg.created_at,
            attendance_status=reg.status,
            check_in_status="not_checked_in",  # Placeholder - would need check-in system
            check_in_time=None,
            ticket_type=None,  # Would need ticket system
            quantity=reg.quantity,
            special_requirements=reg.special_requirements,
            attendee_info=reg.attendee_info
        ))
    
    # Get status counts
    total = len(attendees)
    confirmed = len([a for a in attendees if a.attendance_status == "confirmed"])
    pending = len([a for a in attendees if a.attendance_status == "pending"])
    checked_in = len([a for a in attendees if a.check_in_status == "checked_in"])
    no_show = 0  # Placeholder
    
    return EventAttendeesResponse(
        attendees=attendees,
        total=total,
        confirmed=confirmed,
        pending=pending,
        checked_in=checked_in,
        no_show=no_show
    )


def convert_event_to_institute_out(event: Event) -> InstituteEventOut:
    """Convert Event model to InstituteEventOut"""
    
    # Get current attendee count
    # This would be more efficient with a proper query, but for now we'll use a placeholder
    current_attendees = 0  # Placeholder
    
    return InstituteEventOut(
        id=event.id,
        title=event.title,
        description=event.description,
        event_type="academic",  # Placeholder - would need event type field
        status=event.status.value,
        start_datetime=event.start_datetime,
        end_datetime=event.end_datetime,
        location={},  # Placeholder - would need location parsing
        registration_required=event.registration_required,
        is_public=event.is_public,
        target_audience=[],  # Placeholder - would need target audience field
        budget=None,  # Placeholder - would need budget field
        expected_attendees=None,  # Placeholder
        max_attendees=event.max_attendees,
        current_attendees=current_attendees,
        banner_image_url=event.banner_image_url,
        tags=[],  # Placeholder - would need tags field
        created_at=event.created_at,
        updated_at=event.updated_at
    )
