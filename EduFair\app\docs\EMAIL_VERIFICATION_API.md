# Email Verification System API Documentation

## Overview

The EduFair email verification system provides secure email verification and password reset functionality with 6-digit verification codes. All codes expire after 30 minutes for security and must be manually entered by users to prevent bot attacks.

## Features

- ✅ **Email Verification**: Verify user email addresses with 6-digit codes
- ✅ **Password Reset**: Reset passwords via email with 6-digit codes
- ✅ **Code Expiration**: All codes expire after 30 minutes
- ✅ **Bot Protection**: Manual code entry prevents automated attacks
- ✅ **Rate Limiting**: Max 3 verification emails per hour per user
- ✅ **HTML Email Templates**: Beautiful, responsive email templates
- ✅ **Admin Management**: Admin endpoints for monitoring and cleanup
- ✅ **Security**: IP tracking, user agent logging, code invalidation, user ID validation

## API Endpoints

### 1. Send Verification Email

**POST** `/api/auth/send-verification`

Send verification email to current authenticated user.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
    "message": "Verification email sent successfully",
    "email": "<EMAIL>"
}
```

### 2. Verify Email (API)

**POST** `/api/auth/verify-email`

Verify email using 6-digit verification code via API.

**Headers:**
```
Authorization: Bearer <token>
```

**Request:**
```json
{
    "verification_code": "123456"
}
```

**Response:**
```json
{
    "message": "Email verified successfully",
    "user_id": "user-uuid",
    "email": "<EMAIL>",
    "verified_at": "2025-08-23T12:30:00Z"
}
```

### 3. Resend Verification Email

**POST** `/api/auth/resend-verification`

Resend verification email to current user.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
    "message": "Verification email sent successfully",
    "email": "<EMAIL>"
}
```

### 4. Get Verification Status

**GET** `/api/auth/verification-status`

Get current user's email verification status.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
    "is_email_verified": true,
    "email": "<EMAIL>",
    "verification_sent_at": "2025-08-23T12:00:00Z",
    "verified_at": "2025-08-23T12:30:00Z"
}
```

### 5. Forgot Password

**POST** `/api/auth/forgot-password`

Send password reset email.

**Request:**
```json
{
    "email": "<EMAIL>"
}
```

**Response:**
```json
{
    "message": "If an account with this email exists, a password reset link has been sent."
}
```

### 6. Reset Password

**POST** `/api/auth/reset-password`

Reset password using 6-digit verification code.

**Request:**
```json
{
    "reset_code": "123456",
    "email": "<EMAIL>",
    "new_password": "newpassword123",
    "confirm_password": "newpassword123"
}
```

**Response:**
```json
{
    "message": "Password reset successfully",
    "user_id": "user-uuid"
}
```

## Admin Endpoints

### 7. Verification Statistics

**GET** `/api/auth/admin/verification-stats`

Get email verification statistics (admin only).

**Headers:**
```
Authorization: Bearer <admin-token>
```

**Response:**
```json
{
    "total_users": 1000,
    "verified_users": 850,
    "unverified_users": 150,
    "verification_rate": 85.0,
    "pending_tokens": 25,
    "expired_tokens": 5,
    "recent_verifications": 12
}
```

### 8. Cleanup Expired Tokens

**POST** `/api/auth/admin/cleanup-tokens`

Clean up expired tokens (admin only).

**Headers:**
```
Authorization: Bearer <admin-token>
```

**Response:**
```json
{
    "message": "Successfully cleaned up 15 expired tokens",
    "tokens_cleaned": 15,
    "cleanup_timestamp": "2025-08-23T12:30:00Z"
}
```

## Error Responses

### Common Error Codes

- **400 Bad Request**: Invalid request data or token issues
- **401 Unauthorized**: Missing or invalid authentication
- **404 Not Found**: User or token not found
- **429 Too Many Requests**: Rate limit exceeded
- **500 Internal Server Error**: Server error

### Example Error Response

```json
{
    "detail": "Verification token has expired"
}
```

## Code Security

### Code Properties

- **Format**: 6-digit numeric code (e.g., `123456`)
- **Expiration**: 30 minutes from creation
- **Single Use**: Codes are invalidated after use
- **Type-Specific**: Separate codes for email verification and password reset
- **User Validation**: Codes are tied to specific user IDs for additional security

### Security Features

- **IP Tracking**: Records IP address when code is created
- **User Agent Logging**: Records browser/device information
- **Automatic Cleanup**: Expired codes are automatically cleaned up
- **Rate Limiting**: Max 3 verification emails per hour per user
- **Code Invalidation**: Old codes are invalidated when new ones are created
- **Manual Entry Required**: Codes must be manually typed, preventing bot attacks
- **User ID Validation**: Codes are validated against specific user IDs

## Email Templates

### Verification Email

- **Subject**: "Verify Your EduFair Account"
- **Content**: Welcome message with 6-digit verification code
- **Code Display**: Large, prominent verification code display
- **Instructions**: Step-by-step verification instructions
- **Expiration Warning**: Clear 30-minute expiration notice
- **Responsive Design**: Works on all devices

### Password Reset Email

- **Subject**: "Reset Your EduFair Password"
- **Content**: Password reset instructions with 6-digit reset code
- **Code Display**: Large, prominent reset code display
- **Instructions**: Step-by-step reset instructions
- **Security Notice**: Warning about unauthorized requests
- **Responsive Design**: Works on all devices

## Integration Examples

### Frontend Integration

```javascript
// Send verification email
const sendVerification = async () => {
    const response = await fetch('/api/auth/send-verification', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${userToken}`,
            'Content-Type': 'application/json'
        }
    });
    const result = await response.json();
    console.log(result.message);
};

// Verify email with code
const verifyEmail = async (verificationCode) => {
    const response = await fetch('/api/auth/verify-email', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${userToken}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            verification_code: verificationCode
        })
    });
    const result = await response.json();
    return result;
};

// Check verification status
const checkStatus = async () => {
    const response = await fetch('/api/auth/verification-status', {
        headers: {
            'Authorization': `Bearer ${userToken}`
        }
    });
    const status = await response.json();
    return status.is_email_verified;
};
```

### Backend Integration

```python
# Send verification email during user registration
from Cruds.Auth.email_verification import send_verification_email

# After creating user
success = send_verification_email(
    db=db,
    user_id=new_user.id,
    base_url="https://yourdomain.com",
    request=request
)
```

## Configuration

### Email Settings

Configure in `app/config/email_config.py`:

```python
email_password = "your-app-password"
email_from = "<EMAIL>"
email_host = "smtp.gmail.com"
email_port = 587
```

### Environment Variables

Set these environment variables for production:

```bash
EMAIL_PASSWORD=your-app-password
EMAIL_FROM=<EMAIL>
EMAIL_HOST=smtp.yourdomain.com
EMAIL_PORT=587
```

## Database Schema

### EmailVerificationToken Table

```sql
CREATE TABLE email_verification_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    email VARCHAR NOT NULL,
    token VARCHAR UNIQUE NOT NULL,
    token_type VARCHAR NOT NULL DEFAULT 'email_verification',
    expires_at TIMESTAMP NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP,
    ip_address VARCHAR,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
```

## Best Practices

1. **Always check verification status** before allowing sensitive operations
2. **Use HTTPS** for all verification links
3. **Implement rate limiting** on your frontend
4. **Monitor verification rates** using admin endpoints
5. **Clean up expired tokens** regularly
6. **Use environment variables** for email configuration in production
7. **Test email delivery** in staging environment

## Troubleshooting

### Common Issues

1. **Emails not sending**: Check email configuration and credentials
2. **Tokens expiring too quickly**: Tokens expire after 30 minutes by design
3. **Rate limit errors**: Users can only request 3 emails per hour
4. **HTML not rendering**: Check email client compatibility

### Debugging

Enable debug logging to troubleshoot issues:

```python
import logging
logging.getLogger('services.email_service').setLevel(logging.DEBUG)
```
