from pydantic import BaseModel, Field
from uuid import UUID
from typing import List, Optional

class TeacherBase(BaseModel):
    bio: str = Field(..., description="A brief biography of the teacher")

class TeacherCreate(TeacherBase):
    user_id: UUID = Field(..., description="The unique identifier of the user associated with the teacher profile")

class TeacherOut(TeacherBase):
    user_id: UUID = Field(..., description="The unique identifier of the user associated with the teacher profile")

    class Config:
        from_attributes = True

class TeacherUpdate(BaseModel):
    bio: Optional[str] = Field(None, description="A brief biography of the teacher")

    class Config:
        from_attributes = True