"""
Example script demonstrating how to use the Institute document upload API.

This script shows how to:
1. Update institute profile information
2. Upload documents for verification
3. Handle the API response

Requirements:
- requests library: pip install requests
- Valid institute authentication token
"""

import requests
import json
from pathlib import Path

# Configuration
API_BASE_URL = "http://localhost:8000"
AUTH_TOKEN = "your_institute_auth_token_here"

def update_institute_profile_with_documents():
    """
    Example of updating institute profile with document uploads.
    """
    
    # Endpoint URL
    url = f"{API_BASE_URL}/api/institutes/profile/with-documents"
    
    # Headers
    headers = {
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    # Profile data (form fields)
    data = {
        "institute_name": "Tech University",
        "description": "A leading technology university focused on innovation and research",
        "address": "123 University Ave",
        "city": "San Francisco",
        "state": "CA",
        "postal_code": "94102",
        "website": "https://techuniversity.edu",
        "established_year": 1985,
        "institute_type": "university",
        "accreditation": "WASC Senior College and University Commission",
        "linkedin_url": "https://linkedin.com/company/tech-university",
        "facebook_url": "https://facebook.com/techuniversity",
        "twitter_url": "https://twitter.com/techuniversity"
    }
    
    # Document types and descriptions
    document_types = ["accreditation", "license", "certificate"]
    document_descriptions = [
        "University accreditation certificate from WASC",
        "State operating license",
        "ISO 9001 quality certification"
    ]
    
    # Add document types and descriptions to form data
    for doc_type in document_types:
        data[f"document_types"] = doc_type
    
    for desc in document_descriptions:
        data[f"document_descriptions"] = desc
    
    # Prepare files for upload
    files = []
    
    # Example file paths (replace with actual file paths)
    file_paths = [
        "path/to/accreditation.pdf",
        "path/to/license.pdf", 
        "path/to/certificate.pdf"
    ]
    
    # Check if files exist and prepare for upload
    for file_path in file_paths:
        if Path(file_path).exists():
            files.append(
                ("document_files", (Path(file_path).name, open(file_path, "rb"), "application/pdf"))
            )
        else:
            print(f"Warning: File not found: {file_path}")
    
    try:
        # Make the request
        response = requests.put(url, headers=headers, data=data, files=files)
        
        # Close file handles
        for _, file_tuple in files:
            if len(file_tuple) > 2:
                file_tuple[1].close()
        
        # Check response
        if response.status_code == 200:
            result = response.json()
            print("✅ Profile updated successfully!")
            print(f"Institute: {result['profile']['institute_name']}")
            print(f"Verification Status: {result['verification_status']}")
            print(f"Documents uploaded: {len(result['profile']['documents'])}")
            
            # Print document details
            for doc in result['profile']['documents']:
                print(f"  - {doc['document_type']}: {doc['document_name']} ({'verified' if doc['verified'] else 'pending verification'})")
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")


def upload_single_document():
    """
    Example of uploading a single document separately.
    """
    
    url = f"{API_BASE_URL}/api/upload/institute-document"
    
    headers = {
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    # Form data
    data = {
        "document_type": "certificate",
        "description": "Additional certification document"
    }
    
    # File to upload
    file_path = "path/to/additional_certificate.pdf"
    
    if not Path(file_path).exists():
        print(f"❌ File not found: {file_path}")
        return
    
    try:
        with open(file_path, "rb") as file:
            files = {"file": (Path(file_path).name, file, "application/pdf")}
            
            response = requests.post(url, headers=headers, data=data, files=files)
            
            if response.status_code == 200:
                result = response.json()
                print("✅ Document uploaded successfully!")
                print(f"Document ID: {result['document_id']}")
                print(f"File: {result['file_name']}")
                print(f"Type: {result['document_type']}")
                print(f"Download URL: {result['download_url']}")
            else:
                print(f"❌ Error: {response.status_code}")
                print(response.text)
                
    except Exception as e:
        print(f"❌ Error uploading document: {e}")


def get_profile_status():
    """
    Check the current profile completion status.
    """
    
    url = f"{API_BASE_URL}/api/institutes/profile/status"
    
    headers = {
        "Authorization": f"Bearer {AUTH_TOKEN}"
    }
    
    try:
        response = requests.get(url, headers=headers)
        
        if response.status_code == 200:
            result = response.json()
            print("📊 Profile Status:")
            print(f"  Profile Complete: {'✅' if result['profile_complete'] else '❌'}")
            print(f"  Verification Status: {result['verification_status']}")
            print(f"  Completion: {result['completion_percentage']:.1f}%")
            print(f"  Can Submit for Verification: {'✅' if result['can_submit_for_verification'] else '❌'}")
            
            if result['missing_fields']:
                print(f"  Missing Fields: {', '.join(result['missing_fields'])}")
                
        else:
            print(f"❌ Error: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error checking status: {e}")


if __name__ == "__main__":
    print("🏫 Institute Document Upload API Example")
    print("=" * 50)
    
    # Check if auth token is set
    if AUTH_TOKEN == "your_institute_auth_token_here":
        print("❌ Please set your authentication token in the AUTH_TOKEN variable")
        exit(1)
    
    print("\n1. Checking current profile status...")
    get_profile_status()
    
    print("\n2. Updating profile with documents...")
    update_institute_profile_with_documents()
    
    print("\n3. Uploading additional document...")
    upload_single_document()
    
    print("\n4. Checking updated profile status...")
    get_profile_status()
    
    print("\n✅ Example completed!")
