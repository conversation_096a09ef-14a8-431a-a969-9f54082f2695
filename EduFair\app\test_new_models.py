"""
Test script to verify the new mentor-institute collaboration models work correctly
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import sessionmaker
from config.session import engine
from Models.users import MentorInstituteInvite, MentorInstituteApplication, MentorInstituteAssociation

def test_models():
    """Test that the new models can be queried without errors"""
    
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        print("Testing new mentor-institute collaboration models...")
        
        # Test MentorInstituteInvite model
        print("Testing MentorInstituteInvite model...")
        invites_count = session.query(MentorInstituteInvite).count()
        print(f"✅ MentorInstituteInvite: {invites_count} records found")
        
        # Test MentorInstituteApplication model
        print("Testing MentorInstituteApplication model...")
        applications_count = session.query(MentorInstituteApplication).count()
        print(f"✅ MentorInstituteApplication: {applications_count} records found")
        
        # Test MentorInstituteAssociation model
        print("Testing MentorInstituteAssociation model...")
        associations_count = session.query(MentorInstituteAssociation).count()
        print(f"✅ MentorInstituteAssociation: {associations_count} records found")
        
        # Test specific fields that were causing issues
        print("Testing specific fields...")
        
        # Test start_date field (should work)
        try:
            session.query(MentorInstituteAssociation).filter(
                MentorInstituteAssociation.start_date.isnot(None)
            ).count()
            print("✅ MentorInstituteAssociation.start_date field works correctly")
        except Exception as e:
            print(f"❌ MentorInstituteAssociation.start_date field error: {e}")
        
        # Test applied_at field on application model (should work)
        try:
            session.query(MentorInstituteApplication).filter(
                MentorInstituteApplication.applied_at.isnot(None)
            ).count()
            print("✅ MentorInstituteApplication.applied_at field works correctly")
        except Exception as e:
            print(f"❌ MentorInstituteApplication.applied_at field error: {e}")
        
        # Test invited_at field on invite model (should work)
        try:
            session.query(MentorInstituteInvite).filter(
                MentorInstituteInvite.invited_at.isnot(None)
            ).count()
            print("✅ MentorInstituteInvite.invited_at field works correctly")
        except Exception as e:
            print(f"❌ MentorInstituteInvite.invited_at field error: {e}")
        
        # Test that old fields don't exist on association model (should fail)
        try:
            session.query(MentorInstituteAssociation).filter(
                MentorInstituteAssociation.applied_at.isnot(None)
            ).count()
            print("❌ MentorInstituteAssociation.applied_at field still exists (this should not happen)")
        except AttributeError:
            print("✅ MentorInstituteAssociation.applied_at field correctly removed")
        except Exception as e:
            print(f"❓ Unexpected error testing applied_at: {e}")
        
        try:
            session.query(MentorInstituteAssociation).filter(
                MentorInstituteAssociation.approved_at.isnot(None)
            ).count()
            print("❌ MentorInstituteAssociation.approved_at field still exists (this should not happen)")
        except AttributeError:
            print("✅ MentorInstituteAssociation.approved_at field correctly removed")
        except Exception as e:
            print(f"❓ Unexpected error testing approved_at: {e}")
        
        print("\n🎉 All model tests completed!")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        session.close()

if __name__ == "__main__":
    test_models()
