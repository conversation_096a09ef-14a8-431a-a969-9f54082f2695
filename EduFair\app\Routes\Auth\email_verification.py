"""
API routes for email verification
"""

from fastapi import APIRouter, Depends, HTTPException, Request, Query
from fastapi.responses import HTMLResponse, RedirectResponse
from sqlalchemy.orm import Session
from typing import Optional
from uuid import UUID
from datetime import datetime, timezone

# Import dependencies
from config.session import get_db
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type


# Import CRUD functions
from Cruds.Auth.email_verification import (
    send_verification_email, verify_email_code, resend_verification_email,
    send_password_reset_email, verify_password_reset_code, cleanup_expired_tokens
)

# Import schemas
from Schemas.Auth.email_verification import (
    EmailVerificationRequest, EmailVerificationResponse,
    VerifyEmailRequest, VerifyEmailResponse,
    ResendVerificationRequest, PasswordResetRequest, PasswordResetResponse,
    ResetPasswordRequest, ResetPasswordResponse,
    EmailVerificationStatusOut, AdminEmailVerificationStatsOut
)

# Import models
from Models.users import User, EmailVerificationToken

router = APIRouter(prefix="/auth", tags=["Email Verification"])

@router.post("/send-verification", response_model=EmailVerificationResponse)
def send_verification_email_route(
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Send verification email to current user"""
    current_user = get_current_user(token, db)
    
    # Check if already verified
    if current_user.is_email_verified:
        raise HTTPException(status_code=400, detail="Email is already verified")
    
    # Get base URL from request
    base_url = f"{request.url.scheme}://{request.url.netloc}"
    
    # Send verification email
    success = send_verification_email(db, current_user.id, base_url, request)
    
    if not success:
        raise HTTPException(status_code=500, detail="Failed to send verification email")
    
    return EmailVerificationResponse(
        message="Verification email sent successfully",
        email=current_user.email
    )



@router.post("/verify-email", response_model=VerifyEmailResponse)
def verify_email_api_route(
    request: VerifyEmailRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Verify email using verification code (API endpoint)"""
    current_user = get_current_user(token, db)
    return verify_email_code(db, request.verification_code, current_user.id)

@router.post("/resend-verification", response_model=EmailVerificationResponse)
def resend_verification_email_route(
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Resend verification email to current user"""
    current_user = get_current_user(token, db)
    
    # Get base URL from request
    base_url = f"{request.url.scheme}://{request.url.netloc}"
    
    # Resend verification email
    result = resend_verification_email(db, current_user.id, base_url, request)
    
    return EmailVerificationResponse(
        message=result["message"],
        email=result["email"]
    )

@router.get("/verification-status", response_model=EmailVerificationStatusOut)
def get_verification_status(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get current user's email verification status"""
    current_user = get_current_user(token, db)
    
    # Get latest verification token
    latest_token = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.user_id == current_user.id,
        EmailVerificationToken.token_type == "email_verification"
    ).order_by(EmailVerificationToken.created_at.desc()).first()
    
    return EmailVerificationStatusOut(
        is_email_verified=current_user.is_email_verified,
        email=current_user.email,
        verification_sent_at=latest_token.created_at if latest_token else None,
        verified_at=latest_token.used_at if latest_token and latest_token.is_used else None
    )

@router.post("/forgot-password", response_model=PasswordResetResponse)
def forgot_password_route(
    request_data: PasswordResetRequest,
    request: Request,
    db: Session = Depends(get_db)
):
    """Send password reset email"""
    # Get base URL from request
    base_url = f"{request.url.scheme}://{request.url.netloc}"
    
    # Send password reset email
    result = send_password_reset_email(db, request_data.email, base_url, request)
    
    return PasswordResetResponse(message=result["message"])

@router.post("/reset-password", response_model=ResetPasswordResponse)
def reset_password_route(
    request_data: ResetPasswordRequest,
    db: Session = Depends(get_db)
):
    """Reset password using verification code"""
    # Validate passwords match
    if request_data.new_password != request_data.confirm_password:
        raise HTTPException(status_code=400, detail="Passwords do not match")

    # Verify reset code
    token = verify_password_reset_code(db, request_data.reset_code, request_data.email)

    # Get user
    user = db.query(User).filter(User.id == token.user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Update password
    from passlib.context import CryptContext
    pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
    user.password_hash = pwd_context.hash(request_data.new_password)

    # Mark token as used
    token.is_used = True
    token.used_at = datetime.now(timezone.utc)

    db.commit()

    return ResetPasswordResponse(
        message="Password reset successfully",
        user_id=str(user.id)
    )

# Admin routes
@router.get("/admin/verification-stats", response_model=AdminEmailVerificationStatsOut)
def get_verification_stats(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get email verification statistics (admin only)"""
    from datetime import timedelta
    
    # Get statistics
    total_users = db.query(User).count()
    verified_users = db.query(User).filter(User.is_email_verified == True).count()
    unverified_users = total_users - verified_users
    verification_rate = (verified_users / total_users * 100) if total_users > 0 else 0
    
    # Token statistics
    pending_tokens = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.is_used == False,
        EmailVerificationToken.expires_at > datetime.now(timezone.utc)
    ).count()
    
    expired_tokens = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.expires_at <= datetime.now(timezone.utc)
    ).count()
    
    # Recent verifications (last 24 hours)
    yesterday = datetime.now(timezone.utc) - timedelta(hours=24)
    recent_verifications = db.query(EmailVerificationToken).filter(
        EmailVerificationToken.is_used == True,
        EmailVerificationToken.used_at >= yesterday
    ).count()
    
    return AdminEmailVerificationStatsOut(
        total_users=total_users,
        verified_users=verified_users,
        unverified_users=unverified_users,
        verification_rate=round(verification_rate, 2),
        pending_tokens=pending_tokens,
        expired_tokens=expired_tokens,
        recent_verifications=recent_verifications
    )

@router.post("/admin/cleanup-tokens")
def cleanup_expired_tokens_route(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Clean up expired tokens (admin only)"""
    from datetime import datetime, timezone
    
    tokens_cleaned = cleanup_expired_tokens(db)
    
    return {
        "message": f"Successfully cleaned up {tokens_cleaned} expired tokens",
        "tokens_cleaned": tokens_cleaned,
        "cleanup_timestamp": datetime.now(timezone.utc).isoformat()
    }
