"""
Institute Notification Service

This module handles notifications for institute verification status changes,
profile completion reminders, and other institute-related notifications.
"""

from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import logging

from Models.users import User, InstituteProfile
from config.config import settings

logger = logging.getLogger(__name__)


class InstituteNotificationService:
    """Service for handling institute notifications"""
    
    @staticmethod
    def send_verification_status_notification(
        db: Session,
        institute_user: User,
        old_status: str,
        new_status: str,
        admin_notes: Optional[str] = None
    ) -> bool:
        """
        Send notification when institute verification status changes
        
        Args:
            db: Database session
            institute_user: Institute user object
            old_status: Previous verification status
            new_status: New verification status
            admin_notes: Optional admin notes
            
        Returns:
            bool: True if notification sent successfully
        """
        try:
            profile = institute_user.institute_profile
            if not profile:
                logger.error(f"No institute profile found for user {institute_user.id}")
                return False
            
            # Prepare notification data
            notification_data = {
                "institute_name": profile.institute_name,
                "old_status": old_status,
                "new_status": new_status,
                "admin_notes": admin_notes,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            # Send different notifications based on status
            if new_status == "approved":
                return InstituteNotificationService._send_approval_notification(
                    institute_user, notification_data
                )
            elif new_status == "rejected":
                return InstituteNotificationService._send_rejection_notification(
                    institute_user, notification_data
                )
            elif new_status == "under_review":
                return InstituteNotificationService._send_under_review_notification(
                    institute_user, notification_data
                )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending verification notification: {str(e)}")
            return False
    
    @staticmethod
    def _send_approval_notification(user: User, data: Dict[str, Any]) -> bool:
        """Send approval notification"""
        try:
            # In a real implementation, this would send email/SMS/push notification
            # For now, we'll log the notification
            logger.info(f"NOTIFICATION: Institute {data['institute_name']} approved")
            
            # TODO: Implement actual notification sending
            # - Email notification
            # - In-app notification
            # - SMS notification (if enabled)
            
            return True
        except Exception as e:
            logger.error(f"Error sending approval notification: {str(e)}")
            return False
    
    @staticmethod
    def _send_rejection_notification(user: User, data: Dict[str, Any]) -> bool:
        """Send rejection notification"""
        try:
            logger.info(f"NOTIFICATION: Institute {data['institute_name']} rejected")
            
            # TODO: Implement actual notification sending
            # Include admin notes in the notification
            
            return True
        except Exception as e:
            logger.error(f"Error sending rejection notification: {str(e)}")
            return False
    
    @staticmethod
    def _send_under_review_notification(user: User, data: Dict[str, Any]) -> bool:
        """Send under review notification"""
        try:
            logger.info(f"NOTIFICATION: Institute {data['institute_name']} under review")
            
            # TODO: Implement actual notification sending
            
            return True
        except Exception as e:
            logger.error(f"Error sending under review notification: {str(e)}")
            return False
    
    @staticmethod
    def send_profile_completion_reminder(
        db: Session,
        institute_user: User,
        missing_fields: list
    ) -> bool:
        """
        Send reminder to complete institute profile
        
        Args:
            db: Database session
            institute_user: Institute user object
            missing_fields: List of missing required fields
            
        Returns:
            bool: True if notification sent successfully
        """
        try:
            profile = institute_user.institute_profile
            if not profile:
                return False
            
            logger.info(f"NOTIFICATION: Profile completion reminder for {profile.institute_name}")
            
            # TODO: Implement actual notification sending
            # Include list of missing fields
            
            return True
        except Exception as e:
            logger.error(f"Error sending profile completion reminder: {str(e)}")
            return False
    
    @staticmethod
    def send_welcome_notification(db: Session, institute_user: User) -> bool:
        """
        Send welcome notification to newly registered institute
        
        Args:
            db: Database session
            institute_user: Institute user object
            
        Returns:
            bool: True if notification sent successfully
        """
        try:
            profile = institute_user.institute_profile
            if not profile:
                return False
            
            logger.info(f"NOTIFICATION: Welcome message for {profile.institute_name}")
            
            # TODO: Implement actual notification sending
            # Include onboarding instructions and next steps
            
            return True
        except Exception as e:
            logger.error(f"Error sending welcome notification: {str(e)}")
            return False


def notify_verification_status_change(
    db: Session,
    institute_id: str,
    old_status: str,
    new_status: str,
    admin_notes: Optional[str] = None
) -> bool:
    """
    Convenience function to notify about verification status changes
    
    Args:
        db: Database session
        institute_id: Institute user ID
        old_status: Previous verification status
        new_status: New verification status
        admin_notes: Optional admin notes
        
    Returns:
        bool: True if notification sent successfully
    """
    try:
        institute_user = db.query(User).filter(User.id == institute_id).first()
        if not institute_user:
            logger.error(f"Institute user not found: {institute_id}")
            return False
        
        return InstituteNotificationService.send_verification_status_notification(
            db, institute_user, old_status, new_status, admin_notes
        )
    except Exception as e:
        logger.error(f"Error in notify_verification_status_change: {str(e)}")
        return False


def notify_profile_completion_reminder(
    db: Session,
    institute_id: str,
    missing_fields: list
) -> bool:
    """
    Convenience function to send profile completion reminders
    
    Args:
        db: Database session
        institute_id: Institute user ID
        missing_fields: List of missing required fields
        
    Returns:
        bool: True if notification sent successfully
    """
    try:
        institute_user = db.query(User).filter(User.id == institute_id).first()
        if not institute_user:
            logger.error(f"Institute user not found: {institute_id}")
            return False
        
        return InstituteNotificationService.send_profile_completion_reminder(
            db, institute_user, missing_fields
        )
    except Exception as e:
        logger.error(f"Error in notify_profile_completion_reminder: {str(e)}")
        return False
