# Mentor-Institute Collaboration API Documentation

## Overview

This API provides easy-to-use endpoints for managing mentor-institute collaborations. The system is separated into three main components:

1. **Invitations** - Institutes invite mentors to join their team
2. **Applications** - Mentors apply to join institutes
3. **Collaborations** - Active working relationships between mentors and institutes

## Base URL
```
/api/mentor-collaboration
```

## Authentication
All endpoints require authentication via <PERSON><PERSON> token in the Authorization header:
```
Authorization: Bearer <your_token>
```

## API Endpoints

### 📧 Invitations

#### Send Invitation
**POST** `/invitations/send`

Institute sends an invitation to a mentor.

**Request Body:**
```json
{
  "mentor_email": "<EMAIL>",
  "message": "We'd love to have you join our institute as a mentor for our mathematics program.",
  "hourly_rate": 50.00,
  "hours_per_week": 10,
  "subjects_needed": ["Mathematics", "Physics"],
  "start_date": "2024-01-15T09:00:00Z"
}
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "mentor_email": "<EMAIL>",
  "mentor_name": "<PERSON>",
  "institute_name": "ABC Institute",
  "message": "We'd love to have you join our institute...",
  "status": "pending",
  "hourly_rate": 50.00,
  "hours_per_week": 10,
  "subjects_needed": ["Mathematics", "Physics"],
  "sent_at": "2024-01-10T10:00:00Z",
  "expires_at": "2024-02-09T23:59:59Z",
  "responded_at": null
}
```

#### Get Sent Invitations
**GET** `/invitations/sent?page=1&size=20&status=pending`

Institute retrieves invitations they have sent.

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `size` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (pending, accepted, declined, expired)

#### Get Received Invitations
**GET** `/invitations/received?page=1&size=20&status=pending`

Mentor retrieves invitations they have received.

#### Respond to Invitation
**POST** `/invitations/{invitation_id}/respond`

Mentor responds to an invitation.

**Request Body:**
```json
{
  "accept": true,
  "message": "I'm excited to join your team!",
  "counter_hourly_rate": 55.00,
  "counter_hours_per_week": 8
}
```

**Response:** Returns `CollaborationDetails` if accepted, error if declined.

#### Cancel Invitation
**DELETE** `/invitations/{invitation_id}`

Institute cancels a pending invitation.

### 📝 Applications

#### Submit Application
**POST** `/applications/submit`

Mentor submits an application to join an institute.

**Request Body:**
```json
{
  "institute_id": "123e4567-e89b-12d3-a456-426614174000",
  "message": "I would like to contribute to your institute's mission of quality education.",
  "hourly_rate": 45.00,
  "hours_available": 15,
  "subjects": ["Mathematics", "Computer Science"]
}
```

**Response:**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "mentor_id": "123e4567-e89b-12d3-a456-426614174000",
  "mentor_name": "John Doe",
  "mentor_email": "<EMAIL>",
  "institute_name": "ABC Institute",
  "message": "I would like to contribute...",
  "status": "pending",
  "hourly_rate": 45.00,
  "hours_available": 15,
  "subjects": ["Mathematics", "Computer Science"],
  "submitted_at": "2024-01-10T10:00:00Z",
  "reviewed_at": null,
  "reviewer_message": null
}
```

#### Get Submitted Applications
**GET** `/applications/submitted?page=1&size=20&status=pending`

Mentor retrieves applications they have submitted.

#### Get Received Applications
**GET** `/applications/received?page=1&size=20&status=pending`

Institute retrieves applications they have received.

#### Review Application
**POST** `/applications/{application_id}/review`

Institute reviews a mentor application.

**Request Body:**
```json
{
  "approve": true,
  "message": "Welcome to our team! We're excited to work with you.",
  "approved_hourly_rate": 45.00,
  "approved_hours_per_week": 12
}
```

**Response:** Returns `CollaborationDetails` if approved, error if rejected.

#### Withdraw Application
**DELETE** `/applications/{application_id}`

Mentor withdraws their application.

### 🤝 Collaborations

#### Get Collaborations
**GET** `/collaborations?page=1&size=20&status=active`

Get collaborations for the current user (mentor or institute).

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `size` (optional): Items per page (default: 20, max: 100)
- `status` (optional): Filter by status (active, paused, ended)

**Response:**
```json
{
  "collaborations": [
    {
      "id": "123e4567-e89b-12d3-a456-426614174000",
      "mentor_id": "123e4567-e89b-12d3-a456-426614174000",
      "mentor_name": "John Doe",
      "mentor_email": "<EMAIL>",
      "institute_id": "123e4567-e89b-12d3-a456-426614174000",
      "institute_name": "ABC Institute",
      "status": "active",
      "hourly_rate": 50.00,
      "hours_per_week": 10,
      "subjects": ["Mathematics", "Physics"],
      "started_at": "2024-01-15T09:00:00Z",
      "ended_at": null,
      "total_hours_worked": 120.5,
      "total_earnings": 6025.00
    }
  ],
  "total": 1,
  "page": 1,
  "size": 20,
  "has_next": false,
  "has_prev": false
}
```

#### Update Collaboration
**PUT** `/collaborations/{collaboration_id}`

Update collaboration details.

**Request Body:**
```json
{
  "status": "paused",
  "hourly_rate": 55.00,
  "hours_per_week": 8,
  "end_date": "2024-06-30T23:59:59Z"
}
```

#### End Collaboration
**POST** `/collaborations/{collaboration_id}/end`

End an active collaboration.

### 📊 Summary

#### Get Collaboration Summary
**GET** `/summary`

Get summary statistics for institute.

**Response:**
```json
{
  "total_active_collaborations": 15,
  "total_pending_invitations": 3,
  "total_pending_applications": 7,
  "total_mentors": 15,
  "average_hourly_rate": 47.50,
  "total_hours_this_month": 240.5
}
```

## Status Values

### Invitation Status
- `pending` - Invitation sent, awaiting response
- `accepted` - Mentor accepted the invitation
- `declined` - Mentor declined the invitation
- `expired` - Invitation expired (30 days)

### Application Status
- `pending` - Application submitted, awaiting review
- `approved` - Institute approved the application
- `rejected` - Institute rejected the application

### Collaboration Status
- `active` - Collaboration is currently active
- `paused` - Collaboration is temporarily paused
- `ended` - Collaboration has ended

## Error Responses

All endpoints return standard HTTP status codes:

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found
- `500` - Internal Server Error

Error response format:
```json
{
  "detail": "Error message describing what went wrong"
}
```

## Rate Limiting

API requests are limited to:
- 100 requests per minute for regular operations
- 10 requests per minute for invitation/application submissions

## Best Practices

1. **Check Status**: Always check invitation/application status before responding
2. **Handle Expiry**: Invitations expire after 30 days
3. **Pagination**: Use pagination for large result sets
4. **Error Handling**: Implement proper error handling for all API calls
5. **Authentication**: Keep tokens secure and refresh as needed

## Example Workflows

### Institute Inviting a Mentor
1. POST `/invitations/send` - Send invitation
2. GET `/invitations/sent` - Monitor invitation status
3. GET `/collaborations` - View active collaborations after acceptance

### Mentor Applying to Institute
1. POST `/applications/submit` - Submit application
2. GET `/applications/submitted` - Check application status
3. GET `/collaborations` - View active collaborations after approval

### Managing Active Collaborations
1. GET `/collaborations` - List all collaborations
2. PUT `/collaborations/{id}` - Update terms if needed
3. POST `/collaborations/{id}/end` - End collaboration when complete

## Quick Start Integration

### JavaScript/TypeScript Example

```javascript
// Configuration
const API_BASE = 'https://your-api.com/api/mentor-collaboration';
const token = 'your-auth-token';

const headers = {
  'Authorization': `Bearer ${token}`,
  'Content-Type': 'application/json'
};

// Institute sends invitation
async function sendInvitation(mentorEmail, message, hourlyRate) {
  const response = await fetch(`${API_BASE}/invitations/send`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      mentor_email: mentorEmail,
      message: message,
      hourly_rate: hourlyRate,
      hours_per_week: 10,
      subjects_needed: ['Mathematics']
    })
  });

  if (!response.ok) {
    throw new Error(`Failed to send invitation: ${response.statusText}`);
  }

  return await response.json();
}

// Mentor responds to invitation
async function respondToInvitation(invitationId, accept, message) {
  const response = await fetch(`${API_BASE}/invitations/${invitationId}/respond`, {
    method: 'POST',
    headers,
    body: JSON.stringify({
      accept: accept,
      message: message
    })
  });

  return await response.json();
}

// Get collaborations
async function getCollaborations(page = 1, status = null) {
  const params = new URLSearchParams({ page: page.toString() });
  if (status) params.append('status', status);

  const response = await fetch(`${API_BASE}/collaborations?${params}`, {
    headers
  });

  return await response.json();
}
```

### Python Example

```python
import requests
from typing import Optional, Dict, Any

class MentorCollaborationAPI:
    def __init__(self, base_url: str, token: str):
        self.base_url = base_url
        self.headers = {
            'Authorization': f'Bearer {token}',
            'Content-Type': 'application/json'
        }

    def send_invitation(self, mentor_email: str, message: str,
                       hourly_rate: float = None) -> Dict[Any, Any]:
        """Institute sends invitation to mentor"""
        data = {
            'mentor_email': mentor_email,
            'message': message,
            'hourly_rate': hourly_rate,
            'hours_per_week': 10,
            'subjects_needed': ['Mathematics']
        }

        response = requests.post(
            f'{self.base_url}/invitations/send',
            json=data,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

    def get_collaborations(self, page: int = 1,
                          status: Optional[str] = None) -> Dict[Any, Any]:
        """Get collaborations for current user"""
        params = {'page': page}
        if status:
            params['status'] = status

        response = requests.get(
            f'{self.base_url}/collaborations',
            params=params,
            headers=self.headers
        )
        response.raise_for_status()
        return response.json()

# Usage
api = MentorCollaborationAPI('https://your-api.com/api/mentor-collaboration', 'your-token')

# Send invitation
invitation = api.send_invitation(
    mentor_email='<EMAIL>',
    message='Join our team!',
    hourly_rate=50.0
)

# Get active collaborations
collaborations = api.get_collaborations(status='active')
```

## Testing with cURL

```bash
# Send invitation
curl -X POST "https://your-api.com/api/mentor-collaboration/invitations/send" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "mentor_email": "<EMAIL>",
    "message": "Join our team!",
    "hourly_rate": 50.0,
    "hours_per_week": 10,
    "subjects_needed": ["Mathematics"]
  }'

# Get collaborations
curl -X GET "https://your-api.com/api/mentor-collaboration/collaborations?status=active" \
  -H "Authorization: Bearer your-token"

# Respond to invitation
curl -X POST "https://your-api.com/api/mentor-collaboration/invitations/123/respond" \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{
    "accept": true,
    "message": "I accept!"
  }'
```
