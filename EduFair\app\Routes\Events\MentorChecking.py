from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
from decimal import Decimal

# Import CRUD functions
from Cruds.Events.MentorChecking import (
    assign_mentor_to_competition, get_mentor_assignments, respond_to_assignment,
    get_answers_to_check, submit_mentor_score, get_mentor_checking_statistics
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# Mentor Assignment Routes (for organizers)
@router.post("/competitions/{competition_id}/assign-mentor")
def assign_mentor(
    competition_id: UUID,
    mentor_id: UUID,
    assignment_notes: str = None,
    questions_assigned: List[UUID] = None,
    participants_assigned: List[UUID] = None,
    estimated_hours: float = None,
    hourly_rate: float = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Assign a mentor to check competition submissions (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only teachers and institutes can assign mentors")
    
    hourly_rate_decimal = Decimal(str(hourly_rate)) if hourly_rate else None
    
    return assign_mentor_to_competition(
        db, competition_id, mentor_id, current_user.id,
        assignment_notes, questions_assigned, participants_assigned,
        estimated_hours, hourly_rate_decimal
    )


@router.get("/competitions/{competition_id}/mentor-assignments")
def get_competition_mentor_assignments(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get mentor assignments for a competition (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can view mentor assignments")
    
    from Models.Competitions import CompetitionMentorAssignment
    from Models.Events import Event
    from sqlalchemy.orm import joinedload
    
    # Verify organizer has access
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != current_user.id and competition.institute_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get assignments
    assignments = db.query(CompetitionMentorAssignment).options(
        joinedload(CompetitionMentorAssignment.mentor)
    ).filter(CompetitionMentorAssignment.competition_id == competition_id).all()
    
    assignment_list = []
    for assignment in assignments:
        mentor = assignment.mentor
        assignment_list.append({
            "assignment_id": str(assignment.id),
            "mentor_id": str(mentor.id),
            "mentor_username": mentor.username,
            "mentor_name": mentor.mentor_profile.full_name if mentor.mentor_profile else None,
            "status": assignment.status.value,
            "assigned_at": assignment.assigned_at,
            "accepted_at": assignment.accepted_at,
            "completed_at": assignment.completed_at,
            "total_questions": assignment.total_questions,
            "questions_checked": assignment.questions_checked,
            "progress_percentage": float(assignment.progress_percentage),
            "estimated_hours": float(assignment.estimated_hours) if assignment.estimated_hours else None,
            "hourly_rate": float(assignment.hourly_rate) if assignment.hourly_rate else None,
            "assignment_notes": assignment.assignment_notes
        })
    
    return {
        "competition_id": str(competition_id),
        "assignments": assignment_list
    }


# Mentor Dashboard Routes
@router.get("/mentor/my-assignments")
def get_my_mentor_assignments(
    status: str = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Get current mentor's assignments"""
    current_user = get_current_user(token, db)
    
    return {
        "assignments": get_mentor_assignments(db, current_user.id, status, skip, limit)
    }


@router.post("/mentor/assignments/{assignment_id}/respond")
def respond_to_mentor_assignment(
    assignment_id: UUID,
    accept: bool,
    response_message: str = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Respond to mentor assignment (accept/decline)"""
    current_user = get_current_user(token, db)
    
    return respond_to_assignment(db, assignment_id, current_user.id, accept, response_message)


@router.get("/mentor/answers-to-check")
def get_mentor_answers_to_check(
    competition_id: UUID = Query(None, description="Filter by competition"),
    question_id: UUID = Query(None, description="Filter by question"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Get answers that need to be checked by current mentor"""
    current_user = get_current_user(token, db)
    
    return {
        "answers": get_answers_to_check(db, current_user.id, competition_id, question_id, skip, limit)
    }


@router.post("/mentor/answers/{answer_id}/score")
def submit_answer_score(
    answer_id: UUID,
    mentor_score: float,
    mentor_feedback: str = None,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Submit score and feedback for an answer"""
    current_user = get_current_user(token, db)
    
    mentor_score_decimal = Decimal(str(mentor_score))
    
    return submit_mentor_score(db, answer_id, current_user.id, mentor_score_decimal, mentor_feedback)


@router.get("/mentor/statistics")
def get_mentor_statistics(
    competition_id: UUID = Query(None, description="Filter by competition"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Get mentor's checking statistics"""
    current_user = get_current_user(token, db)
    
    return get_mentor_checking_statistics(db, current_user.id, competition_id)


# Bulk Operations
@router.post("/mentor/answers/bulk-score")
def bulk_score_answers(
    scores: List[dict],  # [{"answer_id": "uuid", "score": 5.0, "feedback": "Good"}]
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Submit scores for multiple answers at once"""
    current_user = get_current_user(token, db)
    
    if not scores or len(scores) == 0:
        raise HTTPException(status_code=400, detail="At least one score is required")
    
    if len(scores) > 50:
        raise HTTPException(status_code=400, detail="Maximum 50 scores can be submitted at once")
    
    results = []
    errors = []
    
    for score_data in scores:
        try:
            answer_id = UUID(score_data["answer_id"])
            mentor_score = Decimal(str(score_data["score"]))
            mentor_feedback = score_data.get("feedback")
            
            result = submit_mentor_score(db, answer_id, current_user.id, mentor_score, mentor_feedback)
            results.append(result)
        except Exception as e:
            errors.append({
                "answer_id": score_data.get("answer_id"),
                "error": str(e)
            })
    
    return {
        "successful_scores": results,
        "errors": errors,
        "total_processed": len(results),
        "total_errors": len(errors)
    }


# Competition-specific mentor routes
@router.get("/competitions/{competition_id}/mentor/dashboard")
def get_mentor_competition_dashboard(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Get mentor dashboard for a specific competition"""
    current_user = get_current_user(token, db)
    
    from Models.Competitions import CompetitionMentorAssignment
    
    # Verify mentor is assigned to this competition
    assignment = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.mentor_id == current_user.id,
        CompetitionMentorAssignment.competition_id == competition_id
    ).first()
    
    if not assignment:
        raise HTTPException(status_code=404, detail="Mentor not assigned to this competition")
    
    # Get answers to check
    answers_to_check = get_answers_to_check(db, current_user.id, competition_id, None, 0, 10)
    
    # Get statistics
    statistics = get_mentor_checking_statistics(db, current_user.id, competition_id)
    
    # Get competition details
    from Models.Events import Event
    competition = db.query(Event).filter(Event.id == competition_id).first()
    
    return {
        "competition": {
            "id": str(competition.id),
            "title": competition.title,
            "start_datetime": competition.start_datetime,
            "end_datetime": competition.end_datetime,
            "status": competition.status.value
        },
        "assignment": {
            "assignment_id": str(assignment.id),
            "status": assignment.status.value,
            "assigned_at": assignment.assigned_at,
            "accepted_at": assignment.accepted_at,
            "total_questions": assignment.total_questions,
            "questions_checked": assignment.questions_checked,
            "progress_percentage": float(assignment.progress_percentage),
            "estimated_hours": float(assignment.estimated_hours) if assignment.estimated_hours else None,
            "hourly_rate": float(assignment.hourly_rate) if assignment.hourly_rate else None
        },
        "answers_to_check": answers_to_check,
        "statistics": statistics
    }


@router.get("/mentor/available-competitions")
def get_available_competitions_for_mentor(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Get competitions available for mentor to check (based on institute associations)"""
    current_user = get_current_user(token, db)
    
    from Models.users import MentorInstituteAssociation
    from Models.Events import Event
    from sqlalchemy.orm import joinedload
    
    # Get institutes mentor is associated with
    associations = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == current_user.id,
        MentorInstituteAssociation.status == "active"
    ).all()
    
    institute_ids = [assoc.institute_id for assoc in associations]
    
    if not institute_ids:
        return {"competitions": []}
    
    # Get competitions from associated institutes
    competitions = db.query(Event).options(
        joinedload(Event.category)
    ).filter(
        Event.institute_id.in_(institute_ids),
        Event.is_competition == True,
        Event.require_mentor_check == True
    ).order_by(Event.start_datetime.desc()).all()
    
    competition_list = []
    for comp in competitions:
        # Check if mentor is already assigned
        from Models.Competitions import CompetitionMentorAssignment
        existing_assignment = db.query(CompetitionMentorAssignment).filter(
            CompetitionMentorAssignment.competition_id == comp.id,
            CompetitionMentorAssignment.mentor_id == current_user.id
        ).first()
        
        competition_list.append({
            "competition_id": str(comp.id),
            "title": comp.title,
            "start_datetime": comp.start_datetime,
            "end_datetime": comp.end_datetime,
            "category": comp.category.name if comp.category else None,
            "status": comp.status.value,
            "is_assigned": existing_assignment is not None,
            "assignment_status": existing_assignment.status.value if existing_assignment else None
        })
    
    return {
        "competitions": competition_list,
        "total_available": len(competition_list)
    }
