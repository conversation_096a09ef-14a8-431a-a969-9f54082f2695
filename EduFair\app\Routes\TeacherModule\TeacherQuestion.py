from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from Schemas.TeacherModule.TeacherQuestion import TeacherQuestionCreate, TeacherQuestionOut
from Cruds.TeacherModule.TeacherQuestion import (
    create_teacher_question,
    get_all_teacher_questions,
    get_teacher_question_by_id,
    delete_teacher_question
)
from config.session import get_db
from typing import List
from config.deps import oauth2_scheme
from config.permission import require_type

router = APIRouter()

@router.get("/", response_model=List[TeacherQuestionOut])
def read_teacher_questions(db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    return get_all_teacher_questions(db)

@router.get("/{id}", response_model=TeacherQuestionOut)
def read_teacher_question(id: UUID, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme)):
    return get_teacher_question_by_id(db, id)

@router.post("/", response_model=TeacherQuestionOut)
def create_teacher_question_endpoint(data: TeacherQuestionCreate, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    return create_teacher_question(db, data)

@router.delete("/{id}", status_code=204)
def delete_teacher_question_endpoint(id: UUID, db: Session = Depends(get_db), token: str = Depends(oauth2_scheme), _ = Depends(require_type("admin"))):
    delete_teacher_question(db, id)
    return None 