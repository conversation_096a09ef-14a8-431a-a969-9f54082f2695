from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, asc, case
from fastapi import HTTPException
from datetime import datetime, timezone
from decimal import Decimal

# Import Models
from Models.Events import Event, EventRegistration, RegistrationStatusEnum
from Models.Competitions import (
    CompetitionResult, CompetitionAnswer, CompetitionQuestion,
    CompetitionSession, CompetitionMentorAssignment
)
from Models.users import User, UserTypeEnum


def calculate_competition_results(
    db: Session,
    competition_id: uuid.UUID,
    organizer_id: uuid.UUID
) -> Dict[str, Any]:
    """Calculate and update competition results"""
    
    # Verify competition exists and organizer has permission
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != organizer_id and competition.institute_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can calculate results")
    
    # Get all participants who submitted answers
    participants = db.query(User).join(CompetitionAnswer).filter(
        CompetitionAnswer.competition_id == competition_id
    ).distinct().all()
    
    results_updated = 0
    
    for participant in participants:
        # Calculate participant's total score
        total_score = db.query(func.sum(CompetitionAnswer.final_score)).filter(
            CompetitionAnswer.competition_id == competition_id,
            CompetitionAnswer.participant_id == participant.id,
            CompetitionAnswer.final_score.isnot(None)
        ).scalar() or 0
        
        # Get maximum possible score
        max_score = db.query(func.sum(CompetitionQuestion.marks)).filter(
            CompetitionQuestion.competition_id == competition_id
        ).scalar() or 0
        
        # Calculate percentage
        percentage = (total_score / max_score * 100) if max_score > 0 else 0
        
        # Get session information
        session = db.query(CompetitionSession).filter(
            CompetitionSession.competition_id == competition_id,
            CompetitionSession.participant_id == participant.id
        ).first()
        
        # Get total time taken
        total_time_seconds = session.total_time_seconds if session else 0
        
        # Count answered questions
        answered_questions = db.query(CompetitionAnswer).filter(
            CompetitionAnswer.competition_id == competition_id,
            CompetitionAnswer.participant_id == participant.id
        ).count()
        
        # Check if result already exists
        existing_result = db.query(CompetitionResult).filter(
            CompetitionResult.competition_id == competition_id,
            CompetitionResult.participant_id == participant.id
        ).first()
        
        if existing_result:
            # Update existing result
            existing_result.total_score = total_score
            existing_result.percentage = percentage
            existing_result.total_time_seconds = total_time_seconds
            existing_result.answered_questions = answered_questions
            existing_result.is_passed = percentage >= (competition.passing_score or 0)
            existing_result.calculated_at = datetime.now(timezone.utc)
        else:
            # Create new result
            result = CompetitionResult(
                competition_id=competition_id,
                participant_id=participant.id,
                total_score=total_score,
                percentage=percentage,
                total_time_seconds=total_time_seconds,
                answered_questions=answered_questions,
                is_passed=percentage >= (competition.passing_score or 0),
                calculated_at=datetime.now(timezone.utc)
            )
            db.add(result)
        
        results_updated += 1
    
    # Calculate rankings
    results = db.query(CompetitionResult).filter(
        CompetitionResult.competition_id == competition_id
    ).order_by(
        desc(CompetitionResult.total_score),
        asc(CompetitionResult.total_time_seconds)
    ).all()
    
    for rank, result in enumerate(results, 1):
        result.rank = rank
    
    db.commit()
    
    return {
        "competition_id": str(competition_id),
        "results_updated": results_updated,
        "total_participants": len(results),
        "calculated_at": datetime.now(timezone.utc)
    }


def get_competition_leaderboard(
    db: Session,
    competition_id: uuid.UUID,
    limit: int = 50,
    include_details: bool = False
) -> List[Dict[str, Any]]:
    """Get competition leaderboard"""
    
    # Verify competition exists
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    # Get results with participant details
    results = db.query(CompetitionResult).options(
        joinedload(CompetitionResult.participant)
    ).filter(
        CompetitionResult.competition_id == competition_id
    ).order_by(
        CompetitionResult.rank.asc()
    ).limit(limit).all()
    
    leaderboard = []
    for result in results:
        participant = result.participant
        
        entry = {
            "rank": result.rank,
            "participant_id": str(participant.id),
            "username": participant.username,
            "total_score": float(result.total_score),
            "percentage": float(result.percentage),
            "is_passed": result.is_passed,
            "total_time_seconds": result.total_time_seconds,
            "answered_questions": result.answered_questions
        }
        
        if include_details:
            # Add session details
            session = db.query(CompetitionSession).filter(
                CompetitionSession.competition_id == competition_id,
                CompetitionSession.participant_id == participant.id
            ).first()
            
            entry.update({
                "session_flagged": session.is_flagged if session else False,
                "violation_count": session.violation_count if session else 0,
                "submitted_at": result.calculated_at
            })
        
        leaderboard.append(entry)
    
    return leaderboard


def get_competition_analytics(
    db: Session,
    competition_id: uuid.UUID,
    organizer_id: uuid.UUID
) -> Dict[str, Any]:
    """Get comprehensive competition analytics"""
    
    # Verify competition exists and organizer has permission
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != organizer_id and competition.institute_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can view analytics")
    
    # Basic statistics
    total_registered = db.query(EventRegistration).filter(
        EventRegistration.event_id == competition_id
    ).count()
    
    total_participated = db.query(CompetitionResult).filter(
        CompetitionResult.competition_id == competition_id
    ).count()
    
    total_passed = db.query(CompetitionResult).filter(
        CompetitionResult.competition_id == competition_id,
        CompetitionResult.is_passed == True
    ).count()
    
    # Score statistics
    score_stats = db.query(
        func.avg(CompetitionResult.total_score).label('avg_score'),
        func.min(CompetitionResult.total_score).label('min_score'),
        func.max(CompetitionResult.total_score).label('max_score'),
        func.stddev(CompetitionResult.total_score).label('std_score')
    ).filter(CompetitionResult.competition_id == competition_id).first()
    
    # Percentage statistics
    percentage_stats = db.query(
        func.avg(CompetitionResult.percentage).label('avg_percentage'),
        func.min(CompetitionResult.percentage).label('min_percentage'),
        func.max(CompetitionResult.percentage).label('max_percentage')
    ).filter(CompetitionResult.competition_id == competition_id).first()
    
    # Time statistics
    time_stats = db.query(
        func.avg(CompetitionResult.total_time_seconds).label('avg_time'),
        func.min(CompetitionResult.total_time_seconds).label('min_time'),
        func.max(CompetitionResult.total_time_seconds).label('max_time')
    ).filter(CompetitionResult.competition_id == competition_id).first()
    
    # Question-wise analytics
    question_analytics = db.query(
        CompetitionQuestion.id,
        CompetitionQuestion.question_text,
        CompetitionQuestion.question_type,
        CompetitionQuestion.marks,
        func.count(CompetitionAnswer.id).label('total_answers'),
        func.avg(CompetitionAnswer.final_score).label('avg_score'),
        func.sum(case([(CompetitionAnswer.final_score == CompetitionQuestion.marks, 1)], else_=0)).label('correct_answers')
    ).join(CompetitionAnswer).filter(
        CompetitionQuestion.competition_id == competition_id
    ).group_by(CompetitionQuestion.id).all()
    
    question_stats = []
    for q in question_analytics:
        accuracy = (q.correct_answers / q.total_answers * 100) if q.total_answers > 0 else 0
        question_stats.append({
            "question_id": str(q.id),
            "question_text": q.question_text[:100] + "..." if len(q.question_text) > 100 else q.question_text,
            "question_type": q.question_type.value,
            "max_marks": q.marks,
            "total_answers": q.total_answers,
            "average_score": float(q.avg_score) if q.avg_score else 0,
            "accuracy_percentage": float(accuracy),
            "difficulty_level": "easy" if accuracy > 80 else "medium" if accuracy > 50 else "hard"
        })
    
    # Security analytics
    flagged_sessions = db.query(CompetitionSession).filter(
        CompetitionSession.competition_id == competition_id,
        CompetitionSession.is_flagged == True
    ).count()
    
    total_violations = db.query(func.sum(CompetitionSession.violation_count)).filter(
        CompetitionSession.competition_id == competition_id
    ).scalar() or 0
    
    # Grade distribution
    grade_distribution = []
    grade_ranges = [
        (90, 100, "A+"),
        (80, 89, "A"),
        (70, 79, "B+"),
        (60, 69, "B"),
        (50, 59, "C"),
        (0, 49, "F")
    ]
    
    for min_pct, max_pct, grade in grade_ranges:
        count = db.query(CompetitionResult).filter(
            CompetitionResult.competition_id == competition_id,
            CompetitionResult.percentage >= min_pct,
            CompetitionResult.percentage <= max_pct
        ).count()
        
        grade_distribution.append({
            "grade": grade,
            "range": f"{min_pct}-{max_pct}%",
            "count": count,
            "percentage": (count / total_participated * 100) if total_participated > 0 else 0
        })
    
    return {
        "competition_id": str(competition_id),
        "competition_title": competition.title,
        "overview": {
            "total_registered": total_registered,
            "total_participated": total_participated,
            "participation_rate": (total_participated / total_registered * 100) if total_registered > 0 else 0,
            "total_passed": total_passed,
            "pass_rate": (total_passed / total_participated * 100) if total_participated > 0 else 0
        },
        "score_statistics": {
            "average_score": float(score_stats.avg_score) if score_stats.avg_score else 0,
            "minimum_score": float(score_stats.min_score) if score_stats.min_score else 0,
            "maximum_score": float(score_stats.max_score) if score_stats.max_score else 0,
            "standard_deviation": float(score_stats.std_score) if score_stats.std_score else 0
        },
        "percentage_statistics": {
            "average_percentage": float(percentage_stats.avg_percentage) if percentage_stats.avg_percentage else 0,
            "minimum_percentage": float(percentage_stats.min_percentage) if percentage_stats.min_percentage else 0,
            "maximum_percentage": float(percentage_stats.max_percentage) if percentage_stats.max_percentage else 0
        },
        "time_statistics": {
            "average_time_minutes": float(time_stats.avg_time / 60) if time_stats.avg_time else 0,
            "minimum_time_minutes": float(time_stats.min_time / 60) if time_stats.min_time else 0,
            "maximum_time_minutes": float(time_stats.max_time / 60) if time_stats.max_time else 0
        },
        "question_analytics": question_stats,
        "security_analytics": {
            "flagged_sessions": flagged_sessions,
            "total_violations": total_violations,
            "security_score": "high" if flagged_sessions / total_participated > 0.1 else "normal" if total_participated > 0 else "unknown"
        },
        "grade_distribution": grade_distribution
    }


def get_participant_detailed_result(
    db: Session,
    competition_id: uuid.UUID,
    participant_id: uuid.UUID,
    requester_id: uuid.UUID
) -> Dict[str, Any]:
    """Get detailed result for a specific participant"""
    
    # Verify competition exists
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    # Check permissions (participant themselves or organizer)
    if (participant_id != requester_id and 
        competition.organizer_id != requester_id and 
        competition.institute_id != requester_id):
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get participant result
    result = db.query(CompetitionResult).options(
        joinedload(CompetitionResult.participant)
    ).filter(
        CompetitionResult.competition_id == competition_id,
        CompetitionResult.participant_id == participant_id
    ).first()
    
    if not result:
        raise HTTPException(status_code=404, detail="Result not found")
    
    # Get detailed answers
    answers = db.query(CompetitionAnswer).options(
        joinedload(CompetitionAnswer.question)
    ).filter(
        CompetitionAnswer.competition_id == competition_id,
        CompetitionAnswer.participant_id == participant_id
    ).order_by(CompetitionAnswer.question.has(CompetitionQuestion.question_order)).all()
    
    answer_details = []
    for answer in answers:
        question = answer.question
        answer_details.append({
            "question_id": str(question.id),
            "question_text": question.question_text,
            "question_type": question.question_type.value,
            "max_marks": question.marks,
            "participant_answer": answer.answer_text,
            "selected_option": answer.selected_option,
            "expected_answer": question.expected_answer,
            "ai_score": float(answer.ai_score) if answer.ai_score else None,
            "mentor_score": float(answer.mentor_score) if answer.mentor_score else None,
            "final_score": float(answer.final_score) if answer.final_score else 0,
            "ai_feedback": answer.ai_feedback,
            "mentor_feedback": answer.mentor_feedback,
            "is_correct": answer.final_score == question.marks,
            "time_taken_seconds": answer.time_taken_seconds
        })
    
    # Get session information
    session = db.query(CompetitionSession).filter(
        CompetitionSession.competition_id == competition_id,
        CompetitionSession.participant_id == participant_id
    ).first()
    
    return {
        "participant": {
            "id": str(result.participant.id),
            "username": result.participant.username
        },
        "result": {
            "rank": result.rank,
            "total_score": float(result.total_score),
            "percentage": float(result.percentage),
            "is_passed": result.is_passed,
            "total_time_seconds": result.total_time_seconds,
            "answered_questions": result.answered_questions,
            "calculated_at": result.calculated_at
        },
        "session": {
            "is_flagged": session.is_flagged if session else False,
            "violation_count": session.violation_count if session else 0,
            "tab_switches": session.tab_switches if session else 0,
            "window_blur_events": session.window_blur_events if session else 0
        } if session else None,
        "answers": answer_details,
        "summary": {
            "total_questions": len(answer_details),
            "correct_answers": len([a for a in answer_details if a["is_correct"]]),
            "accuracy": len([a for a in answer_details if a["is_correct"]]) / len(answer_details) * 100 if answer_details else 0
        }
    }


def export_competition_results(
    db: Session,
    competition_id: uuid.UUID,
    organizer_id: uuid.UUID,
    format: str = "csv"
) -> Dict[str, Any]:
    """Export competition results in various formats"""
    
    # Verify competition exists and organizer has permission
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != organizer_id and competition.institute_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can export results")
    
    # Get all results with participant details
    results = db.query(CompetitionResult).options(
        joinedload(CompetitionResult.participant)
    ).filter(
        CompetitionResult.competition_id == competition_id
    ).order_by(CompetitionResult.rank.asc()).all()
    
    export_data = []
    for result in results:
        participant = result.participant
        
        # Get session info
        session = db.query(CompetitionSession).filter(
            CompetitionSession.competition_id == competition_id,
            CompetitionSession.participant_id == participant.id
        ).first()
        
        export_data.append({
            "rank": result.rank,
            "username": participant.username,
            "email": participant.email,
            "total_score": float(result.total_score),
            "percentage": float(result.percentage),
            "is_passed": result.is_passed,
            "total_time_minutes": result.total_time_seconds / 60 if result.total_time_seconds else 0,
            "answered_questions": result.answered_questions,
            "is_flagged": session.is_flagged if session else False,
            "violation_count": session.violation_count if session else 0,
            "calculated_at": result.calculated_at.isoformat() if result.calculated_at else None
        })
    
    return {
        "competition_id": str(competition_id),
        "competition_title": competition.title,
        "export_format": format,
        "total_records": len(export_data),
        "data": export_data,
        "exported_at": datetime.now(timezone.utc).isoformat()
    }


def get_competition_comparison(
    db: Session,
    competition_ids: List[uuid.UUID],
    organizer_id: uuid.UUID
) -> Dict[str, Any]:
    """Compare multiple competitions"""

    if len(competition_ids) > 5:
        raise HTTPException(status_code=400, detail="Maximum 5 competitions can be compared at once")

    comparison_data = []

    for comp_id in competition_ids:
        # Verify competition exists and organizer has permission
        competition = db.query(Event).filter(
            Event.id == comp_id,
            Event.is_competition == True
        ).first()

        if not competition:
            continue

        if competition.organizer_id != organizer_id and competition.institute_id != organizer_id:
            continue

        # Get basic statistics
        total_participants = db.query(CompetitionResult).filter(
            CompetitionResult.competition_id == comp_id
        ).count()

        avg_score = db.query(func.avg(CompetitionResult.total_score)).filter(
            CompetitionResult.competition_id == comp_id
        ).scalar() or 0

        avg_percentage = db.query(func.avg(CompetitionResult.percentage)).filter(
            CompetitionResult.competition_id == comp_id
        ).scalar() or 0

        pass_count = db.query(CompetitionResult).filter(
            CompetitionResult.competition_id == comp_id,
            CompetitionResult.is_passed == True
        ).count()

        comparison_data.append({
            "competition_id": str(comp_id),
            "title": competition.title,
            "start_datetime": competition.start_datetime,
            "total_participants": total_participants,
            "average_score": float(avg_score),
            "average_percentage": float(avg_percentage),
            "pass_rate": (pass_count / total_participants * 100) if total_participants > 0 else 0,
            "difficulty_level": "easy" if avg_percentage > 80 else "medium" if avg_percentage > 60 else "hard"
        })

    return {
        "competitions": comparison_data,
        "comparison_summary": {
            "total_competitions": len(comparison_data),
            "highest_avg_score": max([c["average_score"] for c in comparison_data]) if comparison_data else 0,
            "lowest_avg_score": min([c["average_score"] for c in comparison_data]) if comparison_data else 0,
            "highest_pass_rate": max([c["pass_rate"] for c in comparison_data]) if comparison_data else 0,
            "lowest_pass_rate": min([c["pass_rate"] for c in comparison_data]) if comparison_data else 0
        }
    }
