from pydantic import BaseModel, Field
from uuid import UUID

class SubjectBase(BaseModel):
    id: UUID = Field(..., description="Unique identifier for the subject")
    name: str = Field(..., description="Name of the subject")
    class Config:
        from_attributes = True

class SubjectCreate(BaseModel):
    name: str = Field(..., description="Name of the subject")

class SubjectUpdate(BaseModel):
    name: str | None = Field(None, description="Name of the subject")