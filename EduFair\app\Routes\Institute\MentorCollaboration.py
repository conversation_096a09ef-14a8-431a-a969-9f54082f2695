"""
Easy-to-use API routes for mentor-institute collaboration
Clear, RESTful endpoints with intuitive naming
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Institute.MentorCollaboration import (
    send_invitation_to_mentor, get_sent_invitations, get_received_invitations,
    respond_to_invitation, cancel_invitation,
    submit_application_to_institute, get_submitted_applications, get_received_applications,
    review_application, withdraw_application,
    get_active_collaborations, update_collaboration, end_collaboration,
    get_collaboration_summary
)

# Import Schemas
from Schemas.Institute.MentorCollaboration import (
    SendInvitationRequest, InvitationDetails, RespondToInvitationRequest,
    SubmitApplicationRequest, ApplicationDetails, ReviewApplicationRequest,
    CollaborationDetails, UpdateCollaborationRequest,
    InvitationListResponse, ApplicationListResponse, CollaborationListResponse,
    CollaborationSummary
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# === INVITATION ENDPOINTS ===

@router.post("/invitations/send", response_model=InvitationDetails)
def send_invitation(
    request: SendInvitationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Send invitation to a mentor
    
    Institute can invite mentors to join their team by providing:
    - Mentor's email address
    - Personal invitation message
    - Proposed terms (hourly rate, hours per week)
    - Required subject areas
    """
    current_user = get_current_user(token, db)
    return send_invitation_to_mentor(db, current_user.id, request)


@router.get("/invitations/sent", response_model=InvitationListResponse)
def get_my_sent_invitations(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page"),
    status: Optional[str] = Query(None, description="Filter by status: pending, accepted, declined, expired"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get invitations sent by institute
    
    Returns paginated list of all invitations sent by the institute
    with optional status filtering.
    """
    current_user = get_current_user(token, db)
    return get_sent_invitations(db, current_user.id, page, size, status)


@router.get("/invitations/received", response_model=InvitationListResponse)
def get_my_received_invitations(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page"),
    status: Optional[str] = Query(None, description="Filter by status: pending, accepted, declined, expired"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get invitations received by mentor
    
    Returns paginated list of all invitations received by the mentor
    with optional status filtering.
    """
    current_user = get_current_user(token, db)
    return get_received_invitations(db, current_user.id, page, size, status)


@router.post("/invitations/{invitation_id}/respond", response_model=CollaborationDetails)
def respond_to_invitation_endpoint(
    invitation_id: UUID,
    response: RespondToInvitationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Respond to an invitation
    
    Mentor can accept or decline an invitation. If accepted,
    a new collaboration is automatically created.
    """
    current_user = get_current_user(token, db)
    return respond_to_invitation(db, current_user.id, invitation_id, response)


@router.delete("/invitations/{invitation_id}")
def cancel_invitation_endpoint(
    invitation_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Cancel a pending invitation
    
    Institute can cancel invitations that haven't been responded to yet.
    """
    current_user = get_current_user(token, db)
    success = cancel_invitation(db, current_user.id, invitation_id)
    return {"message": "Invitation cancelled successfully" if success else "Failed to cancel invitation"}


# === APPLICATION ENDPOINTS ===

@router.post("/applications/submit", response_model=ApplicationDetails)
def submit_application(
    request: SubmitApplicationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Submit application to institute
    
    Mentor can apply to join an institute by providing:
    - Target institute ID
    - Application message
    - Desired terms (hourly rate, available hours)
    - Subject areas they can teach
    """
    current_user = get_current_user(token, db)
    return submit_application_to_institute(db, current_user.id, request)


@router.get("/applications/submitted", response_model=ApplicationListResponse)
def get_my_submitted_applications(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page"),
    status: Optional[str] = Query(None, description="Filter by status: pending, approved, rejected"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Get applications submitted by mentor
    
    Returns paginated list of all applications submitted by the mentor
    with optional status filtering.
    """
    current_user = get_current_user(token, db)
    return get_submitted_applications(db, current_user.id, page, size, status)


@router.get("/applications/received", response_model=ApplicationListResponse)
def get_my_received_applications(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page"),
    status: Optional[str] = Query(None, description="Filter by status: pending, approved, rejected"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get applications received by institute
    
    Returns paginated list of all applications received by the institute
    with optional status filtering.
    """
    current_user = get_current_user(token, db)
    return get_received_applications(db, current_user.id, page, size, status)


@router.post("/applications/{application_id}/review", response_model=CollaborationDetails)
def review_application_endpoint(
    application_id: UUID,
    review: ReviewApplicationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Review a mentor application
    
    Institute can approve or reject applications. If approved,
    a new collaboration is automatically created.
    """
    current_user = get_current_user(token, db)
    return review_application(db, current_user.id, application_id, review)


@router.delete("/applications/{application_id}")
def withdraw_application_endpoint(
    application_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """
    Withdraw an application
    
    Mentor can withdraw applications that haven't been reviewed yet.
    """
    current_user = get_current_user(token, db)
    success = withdraw_application(db, current_user.id, application_id)
    return {"message": "Application withdrawn successfully" if success else "Failed to withdraw application"}


# === COLLABORATION ENDPOINTS ===

@router.get("/collaborations", response_model=CollaborationListResponse)
def get_my_collaborations(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Items per page"),
    status: Optional[str] = Query(None, description="Filter by status: active, paused, ended"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Get collaborations
    
    Returns paginated list of collaborations for the current user
    (mentor or institute) with optional status filtering.
    """
    current_user = get_current_user(token, db)
    return get_active_collaborations(db, current_user.id, current_user.user_type, page, size, status)


@router.put("/collaborations/{collaboration_id}", response_model=CollaborationDetails)
def update_collaboration_endpoint(
    collaboration_id: UUID,
    update_request: UpdateCollaborationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    Update collaboration
    
    Update collaboration details like status, hourly rate, or hours per week.
    Both mentors and institutes can update collaborations they're part of.
    """
    current_user = get_current_user(token, db)
    return update_collaboration(db, collaboration_id, current_user.id, current_user.user_type, update_request)


@router.post("/collaborations/{collaboration_id}/end")
def end_collaboration_endpoint(
    collaboration_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """
    End collaboration
    
    End an active collaboration. Both mentors and institutes can end
    collaborations they're part of.
    """
    current_user = get_current_user(token, db)
    success = end_collaboration(db, collaboration_id, current_user.id, current_user.user_type)
    return {"message": "Collaboration ended successfully" if success else "Failed to end collaboration"}


# === SUMMARY ENDPOINTS ===

@router.get("/summary", response_model=CollaborationSummary)
def get_collaboration_summary_endpoint(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """
    Get collaboration summary
    
    Returns summary statistics for the institute including:
    - Total active collaborations
    - Pending invitations and applications
    - Average hourly rate
    - Total mentors
    """
    current_user = get_current_user(token, db)
    return get_collaboration_summary(db, current_user.id)
