from pydantic import BaseModel, Field
from typing import Optional
from uuid import UUID
from datetime import datetime

class AnnouncementBase(BaseModel):
    title: str = Field(..., min_length=1, max_length=200, description="Title of the announcement")
    content: str = Field(..., min_length=1, max_length=2000, description="Content of the announcement")

class AnnouncementCreate(AnnouncementBase):
    pass

class AnnouncementUpdate(AnnouncementBase):
    title: Optional[str] = Field(None, min_length=1, max_length=200, description="Title of the announcement")
    content: Optional[str] = Field(None, min_length=1, max_length=2000, description="Content of the announcement")

class AnnouncementInDBBase(AnnouncementBase):
    id: UUID
    classroom_id: UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class Announcement(AnnouncementInDBBase):
    pass

class AnnouncementFeedItem(BaseModel):
    """Mixed feed item that can be either an announcement or a task"""
    id: str = Field(..., description="ID of the item")
    title: str = Field(..., description="Title of the item")
    description: str = Field(..., description="Description/content of the item")
    created_at: Optional[datetime] = Field(None, description="Creation timestamp")
    type: str = Field(..., description="Type of item: 'announcement' or 'task'")

    class Config:
        from_attributes = True