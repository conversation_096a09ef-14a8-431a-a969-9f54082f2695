from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Request
from sqlalchemy.orm import Session
from uuid import UUID

# Import security service
from services.CompetitionSecurityService import competition_security_service

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


@router.post("/competitions/{competition_id}/session/start")
def start_competition_session(
    competition_id: UUID,
    request: Request,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Start a secure competition session"""
    current_user = get_current_user(token, db)
    
    # Get client information
    ip_address = request.client.host
    user_agent = request.headers.get("user-agent", "")
    
    return competition_security_service.create_secure_session(
        db, competition_id, current_user.id, ip_address, user_agent
    )


@router.post("/competitions/session/validate")
def validate_competition_session(
    session_token: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Validate and update competition session"""
    
    # Get client information
    ip_address = request.client.host
    user_agent = request.headers.get("user-agent", "")
    
    return competition_security_service.validate_session(
        db, session_token, ip_address, user_agent
    )


@router.post("/competitions/session/security-event")
def record_security_event(
    session_token: str,
    event_type: str,
    event_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """Record a security event during competition"""
    
    success = competition_security_service.record_security_event(
        db, session_token, event_type, event_data
    )
    
    if not success:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return {"message": "Security event recorded"}


@router.post("/competitions/session/end")
def end_competition_session(
    session_token: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """End competition session"""
    
    # Get client information
    submission_ip = request.client.host
    
    return competition_security_service.end_session(
        db, session_token, submission_ip
    )


@router.get("/competitions/{competition_id}/security/statistics")
def get_security_statistics(
    competition_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get security statistics for competition (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can view security statistics")
    
    return competition_security_service.get_session_statistics(
        db, competition_id, current_user.id
    )


@router.get("/competitions/{competition_id}/security/sessions")
def get_competition_sessions(
    competition_id: UUID,
    include_violations: bool = False,
    flagged_only: bool = False,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get competition sessions with security details (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can view session details")
    
    from Models.Events import Event
    from Models.Competitions import CompetitionSession
    from Models.users import User
    from sqlalchemy.orm import joinedload
    
    # Verify organizer has access
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != current_user.id and competition.institute_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get sessions
    query = db.query(CompetitionSession).options(
        joinedload(CompetitionSession.participant)
    ).filter(CompetitionSession.competition_id == competition_id)
    
    if flagged_only:
        query = query.filter(CompetitionSession.is_flagged == True)
    
    sessions = query.order_by(CompetitionSession.started_at.desc()).all()
    
    session_list = []
    for session in sessions:
        session_data = {
            "session_id": str(session.id),
            "participant_id": str(session.participant_id),
            "participant_username": session.participant.username if session.participant else None,
            "started_at": session.started_at,
            "ended_at": session.ended_at,
            "is_active": session.is_active,
            "is_submitted": session.is_submitted,
            "is_flagged": session.is_flagged,
            "total_time_seconds": session.total_time_seconds,
            "remaining_time_seconds": session.remaining_time_seconds,
            "violation_count": session.violation_count,
            "tab_switches": session.tab_switches,
            "window_blur_events": session.window_blur_events,
            "copy_paste_events": session.copy_paste_events,
            "right_click_events": session.right_click_events,
            "ip_address": session.ip_address,
            "submitted_at": session.submitted_at
        }
        
        if include_violations:
            session_data["violations"] = session.violations or []
        
        session_list.append(session_data)
    
    return {
        "competition_id": str(competition_id),
        "sessions": session_list,
        "total_sessions": len(session_list)
    }


@router.post("/competitions/{competition_id}/security/flag-session")
def flag_session_manually(
    competition_id: UUID,
    session_id: UUID,
    reason: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Manually flag a session for review (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can flag sessions")
    
    from Models.Events import Event
    from Models.Competitions import CompetitionSession
    
    # Verify organizer has access
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != current_user.id and competition.institute_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get session
    session = db.query(CompetitionSession).filter(
        CompetitionSession.id == session_id,
        CompetitionSession.competition_id == competition_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Flag session
    session.is_flagged = True
    
    # Add manual flag violation
    violation = {
        "type": "manual_flag",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "reason": reason,
        "flagged_by": str(current_user.id)
    }
    
    existing_violations = session.violations or []
    existing_violations.append(violation)
    session.violations = existing_violations
    session.violation_count = len(existing_violations)
    
    db.commit()
    
    return {"message": "Session flagged successfully"}


@router.post("/competitions/{competition_id}/security/unflag-session")
def unflag_session(
    competition_id: UUID,
    session_id: UUID,
    reason: str,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Remove flag from a session (organizers only)"""
    current_user = get_current_user(token, db)
    
    # Check if user is teacher or institute
    if current_user.user_type not in ["teacher", "institute"]:
        raise HTTPException(status_code=403, detail="Only organizers can unflag sessions")
    
    from Models.Events import Event
    from Models.Competitions import CompetitionSession
    from datetime import datetime, timezone
    
    # Verify organizer has access
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    if competition.organizer_id != current_user.id and competition.institute_id != current_user.id:
        raise HTTPException(status_code=403, detail="Access denied")
    
    # Get session
    session = db.query(CompetitionSession).filter(
        CompetitionSession.id == session_id,
        CompetitionSession.competition_id == competition_id
    ).first()
    
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Unflag session
    session.is_flagged = False
    
    # Add unflag violation
    violation = {
        "type": "manual_unflag",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "reason": reason,
        "unflagged_by": str(current_user.id)
    }
    
    existing_violations = session.violations or []
    existing_violations.append(violation)
    session.violations = existing_violations
    session.violation_count = len(existing_violations)
    
    db.commit()
    
    return {"message": "Session unflagged successfully"}


@router.get("/competitions/session/heartbeat")
def session_heartbeat(
    session_token: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Session heartbeat to maintain active session"""
    
    # Get client information
    ip_address = request.client.host
    user_agent = request.headers.get("user-agent", "")
    
    try:
        session_info = competition_security_service.validate_session(
            db, session_token, ip_address, user_agent
        )
        return {
            "status": "active",
            "remaining_time_seconds": session_info["remaining_time_seconds"],
            "is_flagged": session_info["is_flagged"]
        }
    except HTTPException:
        return {
            "status": "expired",
            "remaining_time_seconds": 0
        }
