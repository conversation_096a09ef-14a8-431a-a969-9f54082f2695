from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Subscriptions import (
    create_subscription_plan, get_subscription_plans, update_subscription_plan,
    create_user_subscription, get_user_subscription, auto_assign_basic_subscription,
    update_teacher_profile_with_subjects, get_teacher_profile_with_subjects,
    search_home_tutors, get_subscription_statistics, update_usage_metrics,
    get_subscription_usage
)

# Import Schemas
from Schemas.Subscriptions import (
    SubscriptionPlanCreate, SubscriptionPlanUpdate, SubscriptionPlanOut,
    UserSubscriptionCreate, UserSubscriptionUpdate, UserSubscriptionOut,
    TeacherProfileUpdate, TeacherProfileOut, HomeTutoringSearchFilter,
    HomeTutoringSearchResponse, SubscriptionStatsOut, SubscriptionUsageOut,
    PlanTypeEnum
)
from Models.users import UserTypeEnum

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# Subscription Plan Management (Admin Only)
@router.post("/plans", response_model=SubscriptionPlanOut)
def create_plan(
    plan_data: SubscriptionPlanCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Create a new subscription plan (admin only)"""
    return create_subscription_plan(db, plan_data)


@router.get("/plans", response_model=List[SubscriptionPlanOut])
def get_plans(
    user_type: Optional[UserTypeEnum] = Query(None, description="Filter by user type"),
    is_active: bool = Query(True, description="Filter by active status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get subscription plans (public)"""
    return get_subscription_plans(db, user_type, is_active, skip, limit)


@router.put("/plans/{plan_id}", response_model=SubscriptionPlanOut)
def update_plan(
    plan_id: UUID,
    plan_update: SubscriptionPlanUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Update a subscription plan (admin only)"""
    return update_subscription_plan(db, plan_id, plan_update)


# User Subscription Management
@router.post("/users/{user_id}/subscription", response_model=UserSubscriptionOut)
def create_user_subscription_route(
    user_id: UUID,
    subscription_data: UserSubscriptionCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Create or update user subscription (admin only)"""
    subscription_data.user_id = user_id
    return create_user_subscription(db, subscription_data)


@router.get("/my-subscription", response_model=Optional[UserSubscriptionOut])
def get_my_subscription(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get current user's subscription"""
    current_user = get_current_user(token, db)
    return get_user_subscription(db, current_user.id)


@router.get("/users/{user_id}/subscription", response_model=Optional[UserSubscriptionOut])
def get_user_subscription_route(
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get user's subscription (admin only)"""
    return get_user_subscription(db, user_id)


@router.post("/auto-assign-basic/{user_id}")
def auto_assign_basic_plan(
    user_id: UUID,
    user_type: UserTypeEnum,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Auto-assign basic subscription to user (admin only)"""
    return auto_assign_basic_subscription(db, user_id, user_type)


# Teacher Profile Management (Enhanced)
@router.put("/teacher/profile", response_model=TeacherProfileOut)
def update_my_teacher_profile(
    profile_update: TeacherProfileUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Update current teacher's profile"""
    current_user = get_current_user(token, db)
    return update_teacher_profile_with_subjects(db, current_user.id, profile_update)


@router.get("/teacher/profile", response_model=TeacherProfileOut)
def get_my_teacher_profile(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Get current teacher's profile"""
    current_user = get_current_user(token, db)
    return get_teacher_profile_with_subjects(db, current_user.id)


@router.get("/teacher/{teacher_id}/profile", response_model=TeacherProfileOut)
def get_teacher_profile(
    teacher_id: UUID,
    db: Session = Depends(get_db)
):
    """Get teacher's profile (public)"""
    return get_teacher_profile_with_subjects(db, teacher_id)


# Home Tutoring System
@router.post("/home-tutors/search", response_model=HomeTutoringSearchResponse)
def search_home_tutors_route(
    search_filter: HomeTutoringSearchFilter,
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Search for home tutors"""
    return search_home_tutors(db, search_filter, skip, limit)


@router.get("/home-tutors", response_model=HomeTutoringSearchResponse)
def get_home_tutors(
    subject_ids: Optional[List[UUID]] = Query(None, description="Subject IDs"),
    latitude: Optional[float] = Query(None, description="Search latitude"),
    longitude: Optional[float] = Query(None, description="Search longitude"),
    radius_km: Optional[int] = Query(10, description="Search radius in km"),
    max_hourly_rate: Optional[float] = Query(None, description="Maximum hourly rate"),
    min_rating: Optional[float] = Query(None, description="Minimum rating"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db)
):
    """Get home tutors with query parameters"""
    from decimal import Decimal
    
    search_filter = HomeTutoringSearchFilter(
        subject_ids=subject_ids,
        latitude=Decimal(str(latitude)) if latitude else None,
        longitude=Decimal(str(longitude)) if longitude else None,
        radius_km=radius_km,
        max_hourly_rate=Decimal(str(max_hourly_rate)) if max_hourly_rate else None,
        min_rating=Decimal(str(min_rating)) if min_rating else None
    )
    
    return search_home_tutors(db, search_filter, skip, limit)


# Subscription Analytics (Admin Only)
@router.get("/analytics/statistics", response_model=SubscriptionStatsOut)
def get_subscription_stats(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get subscription statistics (admin only)"""
    return get_subscription_statistics(db)


# Usage Tracking
@router.post("/usage/update")
def update_usage(
    metric_name: str,
    increment: int = 1,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Update usage metrics for current user"""
    current_user = get_current_user(token, db)
    success = update_usage_metrics(db, current_user.id, metric_name, increment)
    
    if not success:
        raise HTTPException(status_code=404, detail="User subscription not found")
    
    return {"message": "Usage updated successfully"}


@router.get("/usage/my-usage", response_model=Optional[SubscriptionUsageOut])
def get_my_usage(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get current user's subscription usage"""
    current_user = get_current_user(token, db)
    return get_subscription_usage(db, current_user.id)


@router.get("/usage/{user_id}", response_model=Optional[SubscriptionUsageOut])
def get_user_usage(
    user_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get user's subscription usage (admin only)"""
    return get_subscription_usage(db, user_id)


# Subscription Upgrade/Downgrade
@router.post("/upgrade")
def upgrade_subscription(
    plan_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Upgrade current user's subscription"""
    current_user = get_current_user(token, db)
    
    subscription_data = UserSubscriptionCreate(
        user_id=current_user.id,
        plan_id=plan_id
    )
    
    updated_subscription = create_user_subscription(db, subscription_data)
    
    return {
        "message": "Subscription upgraded successfully",
        "subscription": updated_subscription
    }


# Teacher Plan Management
@router.get("/teacher-plans", response_model=List[SubscriptionPlanOut])
def get_teacher_plans(
    db: Session = Depends(get_db)
):
    """Get available plans for teachers"""
    return get_subscription_plans(db, UserTypeEnum.teacher, True)


@router.post("/teacher/enable-home-tutoring")
def enable_home_tutoring(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("teacher"))
):
    """Enable home tutoring for teacher (requires appropriate subscription)"""
    current_user = get_current_user(token, db)
    
    # Check if user has subscription that allows home tutoring
    subscription = get_user_subscription(db, current_user.id)
    if not subscription or not subscription.plan:
        raise HTTPException(status_code=400, detail="No active subscription found")
    
    if not subscription.plan.allows_home_tutoring:
        raise HTTPException(
            status_code=400, 
            detail="Current subscription plan does not allow home tutoring. Please upgrade to a Home Tutor plan."
        )
    
    # Enable home tutoring in teacher profile
    profile_update = TeacherProfileUpdate(offers_home_tutoring=True)
    return update_teacher_profile_with_subjects(db, current_user.id, profile_update)


# Subscription Validation Middleware
def check_subscription_limits(
    db: Session,
    user_id: UUID,
    action: str
) -> bool:
    """Check if user can perform action based on subscription limits"""
    
    usage = get_subscription_usage(db, user_id)
    if not usage:
        return False
    
    # Check specific limits based on action
    if action == "create_classroom" and usage.plan_limits.get("max_classrooms"):
        return usage.current_usage.classrooms_created < usage.plan_limits["max_classrooms"]
    
    if action == "create_exam" and usage.plan_limits.get("max_exams_per_month"):
        return usage.current_usage.exams_created < usage.plan_limits["max_exams_per_month"]
    
    # Add more checks as needed
    return True
