from typing import List, Optional
import uuid
from sqlalchemy.orm import Session
from fastapi import <PERSON>TT<PERSON>Exception
from Models.users import User, TeacherProfile, TeacherRating
from Schemas.users import UserOut
from Schemas.TeacherModule.TeacherProfile import (
    TeacherProfileCreate,
    TeacherProfileOut,
    TeacherProfileUpdate,
    TeacherProfileList,
)
from Models.Classroom import Classroom, StudentClassroom
from sqlalchemy import func

def create_teacher_profile(db: Session, profile: TeacherProfileCreate, user_id: uuid.UUID) -> TeacherProfileOut:
    # Verify the user exists and is a teacher
    user = db.query(User).filter(
        User.id == user_id, 
        User.user_type == "teacher"
    ).first()
    
    if not user:
        raise HTTPException(
            status_code=404,
            detail="Teacher user not found."
        )
    
    # Check if profile already exists for this user
    existing_profile = db.query(TeacherProfile).filter(
        TeacherProfile.teacher_id == user_id
    ).first()
    
    if existing_profile:
        raise HTTPException(
            status_code=400,
            detail="Teacher profile already exists for this user."
        )

    db_profile = TeacherProfile(
        teacher_id=user_id,
        experience_years=getattr(profile, 'experience_years', None),
        bio=getattr(profile, 'bio', None),
        specialization=getattr(profile, 'specialization', None),
        website=getattr(profile, 'website', None),
        office_hours=getattr(profile, 'office_hours', None)
    )
    db.add(db_profile)
    db.commit()
    db.refresh(db_profile)
    
    return TeacherProfileOut.model_validate(db_profile)

def get_teacher_profile_by_id(db: Session, profile_id: uuid.UUID) -> TeacherProfileOut:
    profile = db.query(TeacherProfile).filter(TeacherProfile.id == profile_id).first()
    if not profile:
        raise HTTPException(status_code=200, detail="Teacher profile not found")
    avg_rating = db.query(TeacherRating).filter_by(teacher_id=profile.teacher_id).with_entities(func.avg(TeacherRating.rating)).scalar() or 0.0
    profile.rating = round(float(avg_rating), 1)
    return TeacherProfileOut.model_validate(profile)

def get_teacher_profile_by_user_id(db: Session, user_id: uuid.UUID) -> TeacherProfileOut:
    profile = db.query(TeacherProfile).filter(TeacherProfile.teacher_id == user_id).first()
    if not profile:
        raise HTTPException(status_code=200, detail="Teacher profile not found")
    avg_rating = db.query(TeacherRating).filter_by(teacher_id=profile.teacher_id).with_entities(func.avg(TeacherRating.rating)).scalar() or 0.0
    profile.rating = round(float(avg_rating), 1)
    return TeacherProfileOut.model_validate(profile)

def get_all_teacher_profiles(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    qualification_filter: Optional[str] = None,
    subject_filter: Optional[str] = None
) -> List[TeacherProfileList]:
    query = db.query(TeacherProfile)
    
    if qualification_filter:
        query = query.filter(TeacherProfile.qualification.ilike(f"%{qualification_filter}%"))
    
    if subject_filter:
        query = query.filter(TeacherProfile.subject_specialty.ilike(f"%{subject_filter}%"))
    
    profiles = query.offset(skip).limit(limit).all()
    return [TeacherProfileList.model_validate(profile) for profile in profiles]

def update_teacher_profile(
    db: Session, 
    profile_id: uuid.UUID, 
    profile_update: TeacherProfileUpdate
) -> TeacherProfileOut:
    profile = db.query(TeacherProfile).filter(TeacherProfile.id == profile_id).first()
    if not profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found")
    
    if profile_update.qualification is not None:
        profile.qualification = profile_update.qualification
    if profile_update.subject_specialty is not None:
        profile.subject_specialty = profile_update.subject_specialty
    if profile_update.experience_years is not None:
        profile.experience_years = profile_update.experience_years
    if profile_update.bio is not None:
        profile.bio = profile_update.bio
    
    db.commit()
    db.refresh(profile)
    return TeacherProfileOut.model_validate(profile)

def update_teacher_profile_by_user_id(
    db: Session, 
    user_id: uuid.UUID, 
    profile_update: TeacherProfileUpdate
) -> TeacherProfileOut:
    profile = db.query(TeacherProfile).filter(TeacherProfile.user_id == user_id).first()
    if not profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found")
    
    if profile_update.qualification is not None:
        profile.qualification = profile_update.qualification
    if profile_update.subject_specialty is not None:
        profile.subject_specialty = profile_update.subject_specialty
    if profile_update.experience_years is not None:
        profile.experience_years = profile_update.experience_years
    if profile_update.bio is not None:
        profile.bio = profile_update.bio
    
    db.commit()
    db.refresh(profile)
    return TeacherProfileOut.model_validate(profile)

def delete_teacher_profile(db: Session, profile_id: uuid.UUID) -> None:
    profile = db.query(TeacherProfile).filter(TeacherProfile.id == profile_id).first()
    if not profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found")
    
    db.delete(profile)
    db.commit()

def delete_teacher_profile_by_user_id(db: Session, user_id: uuid.UUID) -> None:
    profile = db.query(TeacherProfile).filter(TeacherProfile.user_id == user_id).first()
    if not profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found")
    
    db.delete(profile)
    db.commit() 

def rate_teacher(db: Session, teacher_id: uuid.UUID, student_id: uuid.UUID, rating: float):
    # 1. Find the teacher's classrooms
    classrooms = db.query(Classroom).filter(Classroom.teacher_id == teacher_id).all()
    classroom_ids = [c.id for c in classrooms]
    if not classroom_ids:
        raise HTTPException(status_code=404, detail="Teacher has no classrooms.")

    # 2. Check if the student is in any of the teacher's classrooms
    student_class = db.query(StudentClassroom).filter(
        StudentClassroom.student_id == student_id,
        StudentClassroom.classroom_id.in_(classroom_ids)
    ).first()
    if not student_class:
        raise HTTPException(status_code=403, detail="You can only rate teachers of your own class.")

    # 3. Get the teacher profile
    profile = db.query(TeacherProfile).filter(TeacherProfile.teacher_id == teacher_id).first()
    if not profile:
        raise HTTPException(status_code=404, detail="Teacher profile not found.")

    # 4. Store/update the individual rating
    if not (0.0 <= rating <= 5.0):
        raise HTTPException(status_code=400, detail="Rating must be between 0.0 and 5.0.")
    rating = round(float(rating), 1)
    teacher_rating = db.query(TeacherRating).filter_by(teacher_id=teacher_id, student_id=student_id).first()
    if teacher_rating:
        teacher_rating.rating = rating
    else:
        teacher_rating = TeacherRating(teacher_id=teacher_id, student_id=student_id, rating=rating)
        db.add(teacher_rating)
    db.commit()

    # 5. Update the average rating on the profile
    avg_rating = db.query(TeacherRating).filter_by(teacher_id=teacher_id).with_entities(func.avg(TeacherRating.rating)).scalar() or 0.0
    profile.rating = round(float(avg_rating), 1)
    db.commit()
    db.refresh(profile)
    return TeacherProfileOut.model_validate(profile) 