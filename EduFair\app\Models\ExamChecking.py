import uuid
from sqlalchemy import Column, String, Integer, DateTime, ForeignKey, Enum as SAEnum, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from .baseModel import BaseModel
from datetime import datetime
import enum

class ExamCheckingStatusEnum(str, enum.Enum):
    pending = "pending"
    checked = "checked"
    rejected = "rejected"

class ExamChecking(BaseModel):
    __tablename__ = 'exam_checkings'
    exam_id = Column(UUID(as_uuid=True), ForeignKey('exams.id'), nullable=False)
    student_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False)
    attempt_id = Column(UUID(as_uuid=True), ForeignKey('student_exam_attempts.id'), nullable=False)
    status = Column(SAEnum(ExamCheckingStatusEnum), default=ExamCheckingStatusEnum.pending, nullable=False)
    total_score = Column(Integer, nullable=True)
    feedback = Column(Text, nullable=True)
    checked_at = Column(DateTime, nullable=True)
    ai_details = Column(Text, nullable=True)  # Raw AI output/logs

    exam = relationship('Exam', backref='exam_checkings')
    student = relationship('User', foreign_keys=[student_id])
    attempt = relationship('StudentExamAttempt', backref='exam_checking')
    questions = relationship('ExamCheckingQuestion', back_populates='exam_checking', cascade='all, delete-orphan')

class ExamCheckingQuestion(BaseModel):
    __tablename__ = 'exam_checking_questions'
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    exam_checking_id = Column(UUID(as_uuid=True), ForeignKey('exam_checkings.id'), nullable=False)
    question_id = Column(UUID(as_uuid=True), ForeignKey('questions.id'), nullable=False)
    student_answer = Column(Text, nullable=True)
    correct_solution = Column(Text, nullable=True)
    marks_awarded = Column(Integer, nullable=True)
    max_marks = Column(Integer, nullable=True)
    remarks = Column(Text, nullable=True)

    exam_checking = relationship('ExamChecking', back_populates='questions')
    question = relationship('Question') 