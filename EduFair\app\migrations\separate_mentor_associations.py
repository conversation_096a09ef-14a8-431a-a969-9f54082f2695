"""
Database migration script to separate mentor-institute associations
Migrates from single overloaded table to three separate tables
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from config.session import SQLALCHEMY_DATABASE_URL
from datetime import datetime, timezone, timedelta

def run_migration():
    """Run the migration to separate mentor associations"""
    
    engine = create_engine(SQLALCHEMY_DATABASE_URL)
    Session = sessionmaker(bind=engine)
    session = Session()
    
    try:
        print("Starting mentor-institute association migration...")
        
        # Step 1: Create new tables
        print("Creating new tables...")
        
        # Create mentor_institute_invites table
        session.execute(text("""
            CREATE TABLE IF NOT EXISTS mentor_institute_invites (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                mentor_id UUID REFERENCES users(id),
                institute_id UUID NOT NULL REFERENCES users(id),
                mentor_email VARCHAR NOT NULL,
                invitation_message TEXT NOT NULL,
                status VARCHAR DEFAULT 'pending',
                proposed_hourly_rate NUMERIC(10,2),
                proposed_hours_per_week INTEGER,
                expertise_areas_needed JSON,
                contract_terms TEXT,
                invited_at TIMESTAMP DEFAULT NOW(),
                expires_at TIMESTAMP,
                responded_at TIMESTAMP,
                response_message TEXT,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW()
            )
        """))
        
        # Create mentor_institute_applications table
        session.execute(text("""
            CREATE TABLE IF NOT EXISTS mentor_institute_applications (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                mentor_id UUID NOT NULL REFERENCES users(id),
                institute_id UUID NOT NULL REFERENCES users(id),
                application_message TEXT NOT NULL,
                status VARCHAR DEFAULT 'pending',
                proposed_hourly_rate NUMERIC(10,2),
                availability_hours INTEGER,
                applied_at TIMESTAMP DEFAULT NOW(),
                responded_at TIMESTAMP,
                response_message TEXT,
                reviewed_by UUID REFERENCES users(id),
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW(),
                UNIQUE(mentor_id, institute_id)
            )
        """))
        
        # Create new mentor_institute_associations table
        session.execute(text("""
            CREATE TABLE IF NOT EXISTS mentor_institute_associations (
                id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                mentor_id UUID NOT NULL REFERENCES users(id),
                institute_id UUID NOT NULL REFERENCES users(id),
                status VARCHAR DEFAULT 'active',
                hourly_rate NUMERIC(10,2),
                hours_per_week INTEGER,
                contract_terms TEXT,
                start_date TIMESTAMP DEFAULT NOW(),
                end_date TIMESTAMP,
                created_at TIMESTAMP DEFAULT NOW(),
                updated_at TIMESTAMP DEFAULT NOW(),
                created_from_invite_id UUID REFERENCES mentor_institute_invites(id),
                created_from_application_id UUID REFERENCES mentor_institute_applications(id),
                UNIQUE(mentor_id, institute_id)
            )
        """))
        
        print("New tables created successfully!")
        
        # Step 2: Check if old table exists and migrate data
        result = session.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'mentor_institute_association'
            )
        """)).scalar()
        
        if result:
            print("Found old mentor_institute_association table. Migrating data...")
            
            # Migrate invitations (institute_invited type)
            session.execute(text("""
                INSERT INTO mentor_institute_invites 
                (id, mentor_id, institute_id, mentor_email, invitation_message, status, 
                 proposed_hourly_rate, invited_at, responded_at, response_message)
                SELECT 
                    id,
                    mentor_id,
                    institute_id,
                    COALESCE((SELECT email FROM users WHERE id = mentor_id), '<EMAIL>'),
                    COALESCE(application_message, 'Invitation from institute'),
                    CASE 
                        WHEN status = 'pending' THEN 'pending'
                        WHEN status = 'approved' THEN 'accepted'
                        WHEN status = 'rejected' THEN 'declined'
                        ELSE 'expired'
                    END,
                    hourly_rate,
                    applied_at,
                    responded_at,
                    response_message
                FROM mentor_institute_association 
                WHERE association_type = 'institute_invited'
                ON CONFLICT (id) DO NOTHING
            """))
            
            # Migrate applications (mentor_applied type)
            session.execute(text("""
                INSERT INTO mentor_institute_applications 
                (id, mentor_id, institute_id, application_message, status, 
                 proposed_hourly_rate, applied_at, responded_at, response_message, reviewed_by)
                SELECT 
                    id,
                    mentor_id,
                    institute_id,
                    COALESCE(application_message, 'Application to join institute'),
                    CASE 
                        WHEN status = 'pending' THEN 'pending'
                        WHEN status = 'approved' THEN 'approved'
                        WHEN status = 'rejected' THEN 'rejected'
                        ELSE 'rejected'
                    END,
                    hourly_rate,
                    applied_at,
                    responded_at,
                    response_message,
                    approved_by
                FROM mentor_institute_association 
                WHERE association_type = 'mentor_applied'
                ON CONFLICT (mentor_id, institute_id) DO NOTHING
            """))
            
            # Migrate active associations (approved status)
            session.execute(text("""
                INSERT INTO mentor_institute_associations 
                (mentor_id, institute_id, status, hourly_rate, start_date, created_at)
                SELECT 
                    mentor_id,
                    institute_id,
                    'active',
                    hourly_rate,
                    COALESCE(approved_at, applied_at, NOW()),
                    COALESCE(approved_at, applied_at, NOW())
                FROM mentor_institute_association 
                WHERE status = 'approved'
                ON CONFLICT (mentor_id, institute_id) DO NOTHING
            """))
            
            print("Data migration completed!")
            
            # Step 3: Rename old table for backup
            session.execute(text("""
                ALTER TABLE mentor_institute_association 
                RENAME TO mentor_institute_association_backup
            """))
            
            print("Old table renamed to mentor_institute_association_backup")
        
        else:
            print("No old table found. Migration completed.")
        
        # Step 4: Create indexes for performance
        print("Creating indexes...")
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_invites_institute_status 
            ON mentor_institute_invites(institute_id, status)
        """))
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_applications_institute_status 
            ON mentor_institute_applications(institute_id, status)
        """))
        
        session.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_associations_institute_status 
            ON mentor_institute_associations(institute_id, status)
        """))
        
        session.commit()
        print("Migration completed successfully!")
        
    except Exception as e:
        session.rollback()
        print(f"Migration failed: {str(e)}")
        raise
    finally:
        session.close()

if __name__ == "__main__":
    run_migration()
