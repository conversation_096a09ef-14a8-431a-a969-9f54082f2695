from typing import List
import uuid
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception
from Models.users import User, UserTypeEnum
from Schemas.users import UserOut
from Schemas.TeacherModule.Classroom import (
    ClassroomCreate,
    ClassroomOut,
    ClassroomOutList,
    ClassroomOutLite,
    ClassroomUpdate,
    StudentClassRequestOut,
    StudentClassroom,
    StudentClassroomCreate,
    StudentClassroomOut,
    StudentClassroomUpdate,
    StudentClassRequest,
    StudentClassRequestCreate,
    StudentClassRequestUpdate,
    ClassroomOutForStudent,
    ClassroomDetailedOut,
)
from Models.Classroom import (
    Classroom,
    StudentClassroom,
    StudentClassRequest,
    RequestStatusEnum,
)
from Models.users import TeacherProfile
from Schemas.TeacherModule.TeacherProfile import TeacherProfileOut
from uuid import UUID

def create_classroom(db: Session, classroom: ClassroomCreate, teacher_id: uuid.UUID) -> ClassroomOut:
    teacher_data = db.query(User).filter(User.id == teacher_id, User.user_type == UserTypeEnum.teacher).first()
    
    if not teacher_data:
        raise HTTPException(
            status_code=404,
            detail="Teacher not found."
        )

    db_classroom = Classroom(
        name=classroom.name,
        description=classroom.description,
        teacher_id=teacher_id
    )
    db.add(db_classroom)
    db.commit()
    db.refresh(db_classroom)
    return ClassroomOut.model_validate(db_classroom)

def get_all_own_classes(db :Session, user_id: uuid.UUID) -> List[ClassroomOutList]:
    user = db.query(User).filter(User.id == user_id, User.user_type == UserTypeEnum.teacher).first()
    if not user:
        raise HTTPException(status_code=404, detail="Teacher not found")
    
    classrooms = db.query(Classroom).filter(Classroom.teacher_id == user_id).all()
    classroom_list = []
    
    for classroom in classrooms:
        # Count students in this classroom
        student_count = db.query(StudentClassroom).filter(
            StudentClassroom.classroom_id == classroom.id
        ).count()
        
        # Count class requests for this classroom
        class_request_count = db.query(StudentClassRequest).filter(
            StudentClassRequest.classroom_id == classroom.id,
            StudentClassRequest.status == RequestStatusEnum.pending
        ).count()
        
        # Create ClassroomOutList object with counts
        classroom_data = {
            "id": classroom.id,
            "name": classroom.name,
            "description": classroom.description,
            "teacher_id": classroom.teacher_id,
            "student_count": student_count,
            "class_request_count": class_request_count
        }
        
        classroom_list.append(ClassroomOutList.model_validate(classroom_data))
    
    return classroom_list

def get_classroom_by_id(db: Session, classroom_id: uuid.UUID) -> ClassroomOut:
    classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
    if not classroom:
        raise HTTPException(status_code=404, detail="Classroom not found")

    # Get all students in the classroom (join StudentClassroom -> User)
    student_users = (
        db.query(User)
        .join(StudentClassroom, StudentClassroom.student_id == User.id)
        .filter(StudentClassroom.classroom_id == classroom_id)
        .all()
    )
    students = [UserOut.model_validate(user) for user in student_users]

    # Get all pending class requests for this classroom, with student user info
    pending_requests = (
        db.query(StudentClassRequest, User)
        .join(User, StudentClassRequest.student_id == User.id)
        .filter(
            StudentClassRequest.classroom_id == classroom_id,
            StudentClassRequest.status == RequestStatusEnum.pending
        )
        .all()
    )
    class_requests = [
        StudentClassRequestOut(
            id=req.id,
            student_id=req.student_id,
            classroom_id=req.classroom_id,
            student_user=UserOut.model_validate(user),
            classroom=req.classroom_id
        )
        for req, user in pending_requests
    ]

    return ClassroomOut(
        id=UUID(str(classroom.id)),
        name=str(classroom.name),
        description=str(classroom.description) if classroom.description is not None else None,
        teacher_id=UUID(str(classroom.teacher_id)),
        students=students,
        class_requests=class_requests
    )

def get_classroom_by_id_for_student(db: Session, classroom_id: uuid.UUID, student_id: uuid.UUID) -> ClassroomOut:
    """
    Get classroom details by ID for a specific student. 
    Only returns data if the student is enrolled in the classroom.
    """
    # First check if the student is enrolled in this classroom
    student_enrollment = db.query(StudentClassroom).filter(
        StudentClassroom.classroom_id == classroom_id,
        StudentClassroom.student_id == student_id
    ).first()
    
    if not student_enrollment:
        raise HTTPException(
            status_code=403, 
            detail="You are not enrolled in this classroom"
        )
    
    classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
    if not classroom:
        raise HTTPException(status_code=404, detail="Classroom not found")

    # Get all students in the classroom (join StudentClassroom -> User)
    student_users = (
        db.query(User)
        .join(StudentClassroom, StudentClassroom.student_id == User.id)
        .filter(StudentClassroom.classroom_id == classroom_id)
        .all()
    )
    students = [UserOut.model_validate(user) for user in student_users]

    # For students, we don't show pending requests (privacy concern)
    # Only show the classroom info and enrolled students
    return ClassroomOut(
        id=UUID(str(classroom.id)),
        name=str(classroom.name),
        description=str(classroom.description) if classroom.description is not None else None,
        teacher_id=UUID(str(classroom.teacher_id)),
        students=students,
        class_requests=[]  # Empty list for students - they can't see pending requests
    )

def get_classroom_detailed_for_student(db: Session, classroom_id: uuid.UUID, student_id: uuid.UUID) -> ClassroomDetailedOut:
    """
    Get detailed classroom information for a student including teacher profile and complete student details.
    Only returns data if the student is enrolled in the classroom.
    """
    # First check if the student is enrolled in this classroom
    student_enrollment = db.query(StudentClassroom).filter(
        StudentClassroom.classroom_id == classroom_id,
        StudentClassroom.student_id == student_id
    ).first()

    if not student_enrollment:
        raise HTTPException(
            status_code=403,
            detail="You are not enrolled in this classroom"
        )

    # Get classroom details
    classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
    if not classroom:
        raise HTTPException(status_code=404, detail="Classroom not found")

    # Get teacher information with profile
    teacher = db.query(User).filter(User.id == classroom.teacher_id).first()
    if not teacher:
        raise HTTPException(status_code=404, detail="Teacher not found")

    teacher_out = UserOut.model_validate(teacher)

    # Get teacher profile if exists
    teacher_profile = db.query(TeacherProfile).filter(TeacherProfile.teacher_id == classroom.teacher_id).first()
    teacher_profile_out = None
    if teacher_profile:
        # Create a copy of teacher profile data and add the user info
        teacher_profile_data = {
            "id": teacher_profile.id,
            "bio": teacher_profile.bio,
            "experience_years": teacher_profile.experience_years,
            "specialization": teacher_profile.specialization,
            "website": teacher_profile.website,
            "office_hours": teacher_profile.office_hours,
            "rating": float(teacher_profile.rating) if teacher_profile.rating else 0.0,
            "created_at": teacher_profile.created_at,
            "updated_at": teacher_profile.updated_at,
            "user": teacher_out
        }
        teacher_profile_out = TeacherProfileOut.model_validate(teacher_profile_data)

    # Get all students in the classroom with complete information
    student_users = (
        db.query(User)
        .join(StudentClassroom, StudentClassroom.student_id == User.id)
        .filter(StudentClassroom.classroom_id == classroom_id)
        .all()
    )
    students = [UserOut.model_validate(user) for user in student_users]

    # Count total students
    student_count = len(students)

    return ClassroomDetailedOut(
        id=UUID(str(classroom.id)),
        name=str(classroom.name),
        description=str(classroom.description) if classroom.description is not None else None,
        teacher_id=UUID(str(classroom.teacher_id)),
        teacher=teacher_out,
        teacher_profile=teacher_profile_out,
        students=students,
        student_count=student_count
    )

def update_classroom(db: Session, classroom_id: uuid.UUID, classroom_update: ClassroomUpdate) -> ClassroomOut:
    classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
    if not classroom:
        raise HTTPException(status_code=404, detail="Classroom not found")
    
    if classroom_update.name is not None:
        classroom.__setattr__('name', classroom_update.name)
    if classroom_update.description is not None:
        classroom.__setattr__('description', classroom_update.description)
    if classroom_update.teacher_id is not None:
        teacher = db.query(User).filter(User.id == classroom_update.teacher_id, User.user_type == UserTypeEnum.teacher).first()
        if not teacher:
            raise HTTPException(status_code=404, detail="Teacher not found")
        classroom.teacher_id = teacher.id
    
    db.commit()
    db.refresh(classroom)
    return ClassroomOut.model_validate(classroom)

def delete_classroom(db: Session, classroom_id: uuid.UUID) -> None:
    classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
    if not classroom:
        raise HTTPException(status_code=404, detail="Classroom not found")
    
    db.delete(classroom)
    db.commit()

def request_student_to_join_classroom(
    db: Session, student_request: StudentClassRequestCreate
) -> StudentClassRequestOut:
    student = db.query(User).filter(User.id == student_request.student_id, User.user_type == UserTypeEnum.student).first()
    if not student:
        raise HTTPException(status_code=404, detail="Student not found")
    
    classroom = db.query(Classroom).filter(Classroom.id == student_request.classroom_id).first()
    if not classroom:
        raise HTTPException(status_code=404, detail="Classroom not found")
    
    # Check if a request already exists for this student and classroom
    existing_request = db.query(StudentClassRequest).filter(
        StudentClassRequest.student_id == student.id,
        StudentClassRequest.classroom_id == classroom.id
    ).first()
    if existing_request:
        raise HTTPException(status_code=400, detail="A request for this student and classroom already exists.")
    
    request = StudentClassRequest(
        student_id=student.id,
        classroom_id=classroom.id
    )
    db.add(request)
    db.commit()
    db.refresh(request)
    return StudentClassRequestOut(
        id=UUID(str(request.id)),
        student_id=UUID(str(request.student_id)),
        classroom_id=UUID(str(request.classroom_id)),
        student_user=UserOut.model_validate(student),
        classroom=UUID(str(request.classroom_id))
    )

def remove_student_from_classroom(
    db: Session, student_classroom: StudentClassroomUpdate
) -> None:
    student_classroom_record = db.query(StudentClassroom).filter(
        StudentClassroom.student_id == student_classroom.student_id,
        StudentClassroom.classroom_id == student_classroom.classroom_id
    ).first()
    
    if not student_classroom_record:
        raise HTTPException(status_code=404, detail="Student not found in classroom")
    
    db.delete(student_classroom_record)
    db.commit()

def reject_student_request(
    db: Session, request_id: uuid.UUID, user_id: uuid.UUID, classroom_id: uuid.UUID
) -> None:
    request = db.query(StudentClassRequest).filter(
        StudentClassRequest.id == request_id,
        StudentClassRequest.student_id == user_id,
        StudentClassRequest.classroom_id == classroom_id
    ).first()
    
    if not request:
        raise HTTPException(status_code=404, detail="Student request not found")

    request.__setattr__('status', RequestStatusEnum.rejected)
    db.commit()

def student_accept_request(db: Session,user_id : uuid.UUID ,request_id : uuid.UUID) -> StudentClassroomOut:
    request = db.query(StudentClassRequest).filter(StudentClassRequest.id == request_id).first()

    student_user = db.query(User).filter(User.id == user_id, User.user_type == UserTypeEnum.student).first()
    if not student_user:
        raise HTTPException(status_code=404, detail="Student not found")
    

    if not request:
        raise HTTPException(status_code=404, detail="Student request not found")
    if getattr(request, 'status', None) != RequestStatusEnum.pending:
        raise HTTPException(status_code=400, detail="Request is not pending")
    if getattr(request, 'student_id', None) != user_id:
        raise HTTPException(status_code=403, detail="You are not authorized to accept this request")
    

    student_classroom = StudentClassroom(
        student_id=request.student_id,
        classroom_id=request.classroom_id
    )
    
    db.add(student_classroom)
    # For SQLAlchemy, assign status using the enum value via instance
    request.__setattr__('status', RequestStatusEnum.accepted)
    db.commit()
    db.refresh(student_classroom)
    
    # Helper to get UUID value from SQLAlchemy InstrumentedAttribute or Column
    def get_uuid(val):
        if hasattr(val, 'hex'):
            return uuid.UUID(val.hex)
        return uuid.UUID(str(val))

    return StudentClassroomOut(
        id=get_uuid(student_classroom.id),
        student_id=get_uuid(student_classroom.student_id),
        classroom_id=get_uuid(student_classroom.classroom_id),
        student=get_uuid(student_classroom.student_id),
        classroom=get_uuid(student_classroom.classroom_id)
    )

def get_student_classrooms(db: Session, student_id: uuid.UUID) -> List[StudentClassroomOut]:
    student = db.query(User).filter(User.id == student_id, User.user_type == UserTypeEnum.student).first()
    if not student:
        raise HTTPException(status_code=404, detail="Student not found")
    
    student_classrooms = db.query(StudentClassroom).filter(StudentClassroom.student_id == student_id).all()
    return [StudentClassroomOut.model_validate(sc) for sc in student_classrooms]

def get_all_students_in_classroom(db: Session, classroom_id: uuid.UUID) -> List[StudentClassroomOut]:
    classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
    if not classroom:
        raise HTTPException(status_code=404, detail="Classroom not found")
    
    students = db.query(StudentClassroom).filter(StudentClassroom.classroom_id == classroom_id).all()
    return [StudentClassroomOut.model_validate(student) for student in students]

def get_all_classroom_students(
    db: Session, user_id: uuid.UUID
) -> List[ClassroomOutForStudent]:
    results = (
        db.query(
            Classroom.id,
            Classroom.name,
            User.username.label("teacher_name"),
            Classroom.teacher_id
        )
        .join(StudentClassroom, StudentClassroom.classroom_id == Classroom.id)
        .join(User, Classroom.teacher_id == User.id)
        .filter(StudentClassroom.student_id == user_id)
        .all()
    )
    if not results:
        raise HTTPException(status_code=200, detail="No classrooms found for this student")
    return [
        ClassroomOutForStudent(
            id=UUID(str(row.id)),
            name=row.name,
            teacher_name=row.teacher_name,
            teacher_id=row.teacher_id
        )
        for row in results
    ]

def get_all_students_in_teacher_classes(
    db: Session,
    teacher_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100,
    username_filter: str | None = None
) -> List[UserOut]:
    """
    Get all students enrolled in any of the teacher's classrooms.
    Uses optimized query with joins to avoid N+1 queries.
    """
    # Verify teacher exists
    teacher = db.query(User).filter(
        User.id == teacher_id,
        User.user_type == UserTypeEnum.teacher
    ).first()
    if not teacher:
        raise HTTPException(status_code=404, detail="Teacher not found")

    # Optimized query: Get all students from teacher's classrooms
    # Uses DISTINCT to avoid duplicates if student is in multiple teacher's classes
    query = (
        db.query(User)
        .join(StudentClassroom, StudentClassroom.student_id == User.id)
        .join(Classroom, StudentClassroom.classroom_id == Classroom.id)
        .filter(
            Classroom.teacher_id == teacher_id,
            User.user_type == UserTypeEnum.student
        )
        .distinct()
    )

    # Apply username filter if provided
    if username_filter:
        query = query.filter(User.username.ilike(f"%{username_filter}%"))

    # Apply pagination
    students = query.offset(skip).limit(limit).all()

    return [UserOut.model_validate(student) for student in students]

def get_all_students_not_in_classroom(
    db: Session,
    classroom_id: uuid.UUID,
    skip: int = 0,
    limit: int = 10,
    username_filter: str | None = None
) -> List[UserOut]:
    classroom = db.query(Classroom).filter(Classroom.id == classroom_id).first()
    if not classroom:
        raise HTTPException(status_code=404, detail="Classroom not found")

    # Subquery: students already in the classroom
    subq_in_class = db.query(StudentClassroom.student_id).filter(
        StudentClassroom.classroom_id == classroom_id
    )
    # Subquery: students with a pending or existing request for this classroom
    subq_requested = db.query(StudentClassRequest.student_id).filter(
        StudentClassRequest.classroom_id == classroom_id,
        StudentClassRequest.status == RequestStatusEnum.pending
    )
    # Exclude both
    query = db.query(User).filter(
        User.user_type == UserTypeEnum.student,
        ~User.id.in_(subq_in_class),
        ~User.id.in_(subq_requested)
    )
    if username_filter:
        query = query.filter(User.username.ilike(f"%{username_filter}%"))
    students = query.offset(skip).limit(limit).all()
    return [UserOut.model_validate(student) for student in students]

def get_all_requests_for_student(db: Session, student_id: uuid.UUID) -> List[StudentClassRequestOut]:
    try:
        # Query StudentClassRequest with joined User data
        requests_with_users = (
            db.query(StudentClassRequest, User)
            .join(User, StudentClassRequest.student_id == User.id)
            .filter(StudentClassRequest.student_id == student_id)
            .all()
        )

        result = []
        for request, user in requests_with_users:
            try:
                result.append(StudentClassRequestOut(
                    id=UUID(str(request.id)),
                    student_id=UUID(str(request.student_id)),
                    classroom_id=UUID(str(request.classroom_id)),
                    student_user=UserOut.model_validate(user),
                    classroom=UUID(str(request.classroom_id))
                ))
            except Exception as e:
                # Log the specific error for this request
                print(f"Error processing request {request.id}: {str(e)}")
                continue

        return result
    except Exception as e:
        print(f"Error in get_all_requests_for_student: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")

def get_all_sent_requests_by_teacher(db: Session, teacher_id: uuid.UUID) -> List[StudentClassRequestOut]:
    """
    Get all requests sent by a teacher to students across all their classrooms.
    """
    try:
        # Query StudentClassRequest with joined User and Classroom data
        # Filter by teacher_id through the classroom relationship
        requests_with_users = (
            db.query(StudentClassRequest, User, Classroom)
            .join(User, StudentClassRequest.student_id == User.id)
            .join(Classroom, StudentClassRequest.classroom_id == Classroom.id)
            .filter(Classroom.teacher_id == teacher_id)
            .all()
        )

        result = []
        for request, user, classroom in requests_with_users:
            try:
                result.append(StudentClassRequestOut(
                    id=UUID(str(request.id)),
                    student_id=UUID(str(request.student_id)),
                    classroom_id=UUID(str(request.classroom_id)),
                    student_user=UserOut.model_validate(user),
                    classroom=UUID(str(request.classroom_id))
                ))
            except Exception as e:
                # Log the specific error for this request
                print(f"Error processing request {request.id}: {str(e)}")
                continue

        return result
    except Exception as e:
        print(f"Error in get_all_sent_requests_by_teacher: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
