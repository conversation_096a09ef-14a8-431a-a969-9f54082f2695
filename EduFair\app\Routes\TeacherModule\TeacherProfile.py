from typing import List, Optional
from fastapi import APIRouter, Depends, Query, Response, HTTPException
from sqlalchemy.orm import Session
from uuid import UUID
from config.session import get_db
from config.security import oauth2_scheme
from Schemas.TeacherModule.TeacherProfile import (
    Teacher<PERSON><PERSON><PERSON>le<PERSON><PERSON>,
    TeacherProfileOut,
    TeacherProfileUpdate,
    TeacherProfileList,
)
from Cruds.TeacherModule.TeacherProfile import (
    create_teacher_profile,
    get_teacher_profile_by_id,
    get_teacher_profile_by_user_id,
    get_all_teacher_profiles,
    update_teacher_profile,
    update_teacher_profile_by_user_id,
    delete_teacher_profile,
    delete_teacher_profile_by_user_id,
    rate_teacher,
)
from config.permission import require_type
from config.deps import get_current_user

router = APIRouter()

@router.post("/", response_model=TeacherProfileOut)
def create_profile(
    profile: TeacherProfileCreate,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme)
):
    """Create a new teacher profile"""
    current_user = get_current_user(token, db)
    return create_teacher_profile(db, profile, current_user.id)

@router.get("/teacherProfile/{user_id}", response_model=TeacherProfileOut)
def get_profile_by_user_id(
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme)
):
    current_user = get_current_user(token, db)
    """Get teacher profile by user ID"""
    return get_teacher_profile_by_user_id(db, current_user.id)

@router.put("/user/{user_id}", response_model=TeacherProfileOut)
def update_profile_by_user_id(
    profile_update: TeacherProfileUpdate,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme)
):
    current_user = get_current_user(token, db)
    """Update teacher profile by user ID"""
    return update_teacher_profile_by_user_id(db,current_user.id , profile_update)

@router.delete("/user/{user_id}", status_code=204)
def delete_profile_by_user_id(
    user_id: UUID,
    db: Session = Depends(get_db)
):
    """Delete teacher profile by user ID"""
    delete_teacher_profile_by_user_id(db, user_id)
    return Response(status_code=204)

@router.get("/{profile_id}", response_model=TeacherProfileOut)
def get_profile_by_id(
    profile_id: UUID,
    db: Session = Depends(get_db)
):
    """Get teacher profile by ID"""
    return get_teacher_profile_by_id(db, profile_id)

@router.put("/", response_model=TeacherProfileOut)
def update_profile(
    profile_update: TeacherProfileUpdate,
    db: Session = Depends(get_db),
    token = Depends(oauth2_scheme)
):
    """Update the current teacher's profile"""
    current_user = get_current_user(token, db)
    return update_teacher_profile_by_user_id(db, current_user.id, profile_update)

@router.delete("/{profile_id}", status_code=204)
def delete_profile(
    profile_id: UUID,
    db: Session = Depends(get_db)
):
    """Delete teacher profile by ID"""
    delete_teacher_profile(db, profile_id)
    return Response(status_code=204)

@router.get("/", response_model=List[TeacherProfileList])
def get_all_profiles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    qualification: Optional[str] = Query(None, description="Filter by qualification"),
    subject: Optional[str] = Query(None, description="Filter by subject specialty"),
    db: Session = Depends(get_db)
):
    """Get all teacher profiles with optional filtering"""
    return get_all_teacher_profiles(
        db, 
        skip=skip, 
        limit=limit,
        qualification_filter=qualification,
        subject_filter=subject
    ) 

@router.post("/rate/{teacher_id}", response_model=TeacherProfileOut)
def rate_teacher_route(
    teacher_id: UUID,
    rating: float,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("student")),
):
    """
    Allow a student to rate a teacher. Only students in the teacher's class can rate.
    The student_id should be extracted from the token (get_current_user).
    """
    current_user = get_current_user(token, db)
    return rate_teacher(db, teacher_id, current_user.id, rating) 