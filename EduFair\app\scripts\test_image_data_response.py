"""
Test script to verify that institute profile returns actual image data instead of URLs.

This script tests:
1. Profile endpoint returns image data
2. Image data is properly base64 encoded
3. All image fields (profile_picture, logo, banner) are included
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import base64
from fastapi.testclient import Test<PERSON>lient
from sqlalchemy.orm import Session
from main import app
from config.session import get_db
from Models.users import User, UserTypeEnum, InstituteProfile
from Schemas.Institute.Institute import InstituteProfileWithDocumentsRequest, ImageData
import uuid
import bcrypt

client = TestClient(app)

def create_test_institute_user():
    """Create a test institute user with profile and images"""
    try:
        db = next(get_db())
        
        # Use unique values
        test_id = str(uuid.uuid4())[:8]
        test_email = f"test_image_data_{test_id}@example.com"
        test_username = f"test_image_data_{test_id}"
        test_mobile = f"+123456789{test_id[:1]}"
        
        # Clean up any existing test users
        existing_users = db.query(User).filter(
            (User.email == test_email) | 
            (User.username == test_username) | 
            (User.mobile == test_mobile)
        ).all()
        for user in existing_users:
            profile = db.query(InstituteProfile).filter(InstituteProfile.user_id == user.id).first()
            if profile:
                db.delete(profile)
            db.delete(user)
        db.commit()
        
        # Create new test user
        hashed_password = bcrypt.hashpw("TestPass123!".encode('utf-8'), bcrypt.gensalt())
        
        user = User(
            id=uuid.uuid4(),
            username=test_username,
            email=test_email,
            mobile=test_mobile,
            password_hash=hashed_password.decode('utf-8'),
            country="United States",
            user_type=UserTypeEnum.institute,
            is_email_verified=True,
            is_mobile_verified=True
        )
        
        db.add(user)
        db.commit()
        db.refresh(user)
        
        print(f"✅ Created test institute user: {user.id}")
        
        # Generate auth token
        from config.security import create_access_token
        token = create_access_token(data={"sub": test_email})
        
        return token, user.id
        
    except Exception as e:
        print(f"❌ Error creating test institute user: {e}")
        db.rollback()
        return None, None
    finally:
        db.close()

def create_test_image_data(filename="test_image.png"):
    """Create test image data in base64 format"""
    # Create a simple test image content (PNG header + minimal data)
    png_header = b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde'
    test_content = png_header + b'\x00' * 50  # Minimal PNG data
    
    return base64.b64encode(test_content).decode()

def test_profile_with_image_data():
    """Test that profile endpoint returns actual image data"""
    print("🧪 Testing profile endpoint with image data...")
    
    # Create test user and get token
    token, user_id = create_test_institute_user()
    if not token:
        return False
    
    try:
        headers = {"Authorization": f"Bearer {token}"}
        
        # First, create a profile with logo and banner
        logo_data = ImageData(
            filename="test_logo.png",
            content_type="image/png",
            data=create_test_image_data("test_logo.png")
        )
        
        banner_data = ImageData(
            filename="test_banner.png",
            content_type="image/png",
            data=create_test_image_data("test_banner.png")
        )
        
        # Create profile with images
        request_data = InstituteProfileWithDocumentsRequest(
            institute_name="Test University with Images",
            description="Testing image data response",
            city="Test City",
            institute_type="university",
            logo=logo_data,
            banner=banner_data
        ).model_dump()
        
        # Upload profile with images
        response = client.put(
            "/api/institutes/profile/with-documents",
            headers=headers,
            json=request_data
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to create profile: {response.status_code}: {response.text}")
            return False
        
        print("✅ Profile created successfully")
        
        # Now test the profile endpoint
        response = client.get(
            "/api/institutes/profile",
            headers=headers
        )
        
        if response.status_code != 200:
            print(f"❌ Failed to get profile: {response.status_code}: {response.text}")
            return False
        
        result = response.json()
        
        # Check that we have the expected structure
        if 'user' not in result or 'profile' not in result:
            print("❌ Response missing user or profile data")
            return False
        
        # Check logo data
        profile = result['profile']
        if 'logo' in profile and profile['logo']:
            logo = profile['logo']
            if 'data' in logo and logo['data']:
                print("✅ Logo data found in response")
                print(f"   Filename: {logo.get('filename')}")
                print(f"   Content Type: {logo.get('content_type')}")
                print(f"   Data Length: {len(logo['data'])} characters")
            else:
                print("❌ Logo data missing or empty")
                return False
        else:
            print("⚠️ No logo data in response")
        
        # Check banner data
        if 'banner' in profile and profile['banner']:
            banner = profile['banner']
            if 'data' in banner and banner['data']:
                print("✅ Banner data found in response")
                print(f"   Filename: {banner.get('filename')}")
                print(f"   Content Type: {banner.get('content_type')}")
                print(f"   Data Length: {len(banner['data'])} characters")
            else:
                print("❌ Banner data missing or empty")
                return False
        else:
            print("⚠️ No banner data in response")
        
        # Check profile picture data (if any)
        user = result['user']
        if 'profile_picture_data' in user and user['profile_picture_data']:
            profile_pic = user['profile_picture_data']
            if 'data' in profile_pic and profile_pic['data']:
                print("✅ Profile picture data found in response")
                print(f"   Filename: {profile_pic.get('filename')}")
                print(f"   Content Type: {profile_pic.get('content_type')}")
                print(f"   Data Length: {len(profile_pic['data'])} characters")
            else:
                print("⚠️ Profile picture data field exists but is empty")
        else:
            print("⚠️ No profile picture data in response (expected if no profile picture uploaded)")
        
        print("✅ Profile endpoint returns image data successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def cleanup_test_data():
    """Clean up test data"""
    try:
        db = next(get_db())
        
        # Delete test users and their profiles
        test_patterns = ["test_image_data_"]
        for pattern in test_patterns:
            users = db.query(User).filter(User.email.like(f"%{pattern}%")).all()
            for user in users:
                profile = db.query(InstituteProfile).filter(InstituteProfile.user_id == user.id).first()
                if profile:
                    db.delete(profile)
                db.delete(user)
        
        db.commit()
        print("✅ Cleaned up test data")
        
    except Exception as e:
        print(f"⚠️ Warning: Could not clean up test data: {e}")
    finally:
        db.close()

def main():
    """Main test function"""
    print("🚀 Testing institute profile image data response...")
    print("=" * 60)
    
    success_count = 0
    total_tests = 1
    
    # Test: Profile with image data
    if test_profile_with_image_data():
        success_count += 1
    
    print("\n" + "-" * 40)
    
    # Cleanup
    cleanup_test_data()
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {success_count}/{total_tests} tests passed")
    
    if success_count == total_tests:
        print("✅ All tests passed! Profile returns image data correctly.")
        return 0
    else:
        print("❌ Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    exit(main())
