"""
Hardcoded subscription plans configuration.
These plans are used as the source of truth for subscription features and limits.
"""

from enum import Enum
from typing import Dict, List, Optional, Any
from Models.users import UserTypeEnum


class PlanType(str, Enum):
    BASIC = "basic"
    PREMIUM = "premium"
    PRO = "pro"
    HOME_TUTOR = "home_tutor"


class FeaturePermission(str, Enum):
    # General Features
    CREATE_CLASSROOM = "create_classroom"
    CREATE_EXAM = "create_exam"
    CREATE_COMPETITION = "create_competition"
    AI_QUESTION_GENERATION = "ai_question_generation"
    ADVANCED_ANALYTICS = "advanced_analytics"
    PRIORITY_SUPPORT = "priority_support"
    
    # Teacher Features
    HOME_TUTORING = "home_tutoring"
    BULK_OPERATIONS = "bulk_operations"
    CUSTOM_BRANDING = "custom_branding"
    API_ACCESS = "api_access"
    
    # Institute Features
    MENTOR_MANAGEMENT = "mentor_management"
    TEACHER_MANAGEMENT = "teacher_management"
    INSTITUTE_COMPETITIONS = "institute_competitions"
    
    # Student Features
    PREMIUM_EVENTS = "premium_events"
    ADVANCED_PROFILE = "advanced_profile"
    
    # Mentor Features
    PRIORITY_ASSIGNMENTS = "priority_assignments"
    PAYMENT_PROCESSING = "payment_processing"


# Hardcoded subscription plans
SUBSCRIPTION_PLANS = {
    # STUDENT PLANS
    UserTypeEnum.student: {
        PlanType.BASIC: {
            "id": "student_basic",
            "name": "Student Basic",
            "description": "Free basic plan for students",
            "price": 0,
            "duration_days": 365,
            "features": [
                FeaturePermission.ADVANCED_PROFILE
            ],
            "limits": {
                "events_per_month": 10,
                "competitions_per_month": 5
            },
            "is_default": True
        }
    },
    
    # TEACHER PLANS
    UserTypeEnum.teacher: {
        PlanType.BASIC: {
            "id": "teacher_basic",
            "name": "Teacher Basic",
            "description": "Basic plan for teachers - 1 month trial",
            "price": 0,
            "duration_days": 30,
            "features": [
                FeaturePermission.CREATE_CLASSROOM,
                FeaturePermission.CREATE_EXAM
            ],
            "limits": {
                "max_classrooms": 2,
                "max_students_per_classroom": 30,
                "max_exams_per_month": 10,
                "max_questions_per_exam": 50
            },
            "is_default": True
        },
        PlanType.PREMIUM: {
            "id": "teacher_premium",
            "name": "Teacher Premium",
            "description": "Premium plan for teachers with advanced features",
            "price": 2999,  # $29.99
            "duration_days": 30,
            "features": [
                FeaturePermission.CREATE_CLASSROOM,
                FeaturePermission.CREATE_EXAM,
                FeaturePermission.CREATE_COMPETITION,
                FeaturePermission.AI_QUESTION_GENERATION,
                FeaturePermission.ADVANCED_ANALYTICS,
                FeaturePermission.BULK_OPERATIONS,
                FeaturePermission.PRIORITY_SUPPORT
            ],
            "limits": {
                "max_classrooms": 20,
                "max_students_per_classroom": 100,
                "max_exams_per_month": 50,
                "max_questions_per_exam": 200,
                "ai_questions_per_month": 500
            }
        },
        PlanType.HOME_TUTOR: {
            "id": "teacher_home_tutor",
            "name": "Teacher Home Tutor",
            "description": "Special plan for home tutoring services",
            "price": 4999,  # $49.99
            "duration_days": 30,
            "features": [
                FeaturePermission.CREATE_CLASSROOM,
                FeaturePermission.CREATE_EXAM,
                FeaturePermission.CREATE_COMPETITION,
                FeaturePermission.AI_QUESTION_GENERATION,
                FeaturePermission.ADVANCED_ANALYTICS,
                FeaturePermission.HOME_TUTORING,
                FeaturePermission.BULK_OPERATIONS,
                FeaturePermission.PRIORITY_SUPPORT,
                FeaturePermission.PAYMENT_PROCESSING
            ],
            "limits": {
                "max_classrooms": 50,
                "max_students_per_classroom": 200,
                "max_exams_per_month": 100,
                "max_questions_per_exam": 500,
                "ai_questions_per_month": 1000,
                "home_tutor_listings": 10
            }
        }
    },
    
    # INSTITUTE PLANS
    UserTypeEnum.institute: {
        PlanType.BASIC: {
            "id": "institute_basic",
            "name": "Institute Basic",
            "description": "Basic plan for educational institutes - 1 month trial",
            "price": 0,
            "duration_days": 30,
            "features": [
                FeaturePermission.TEACHER_MANAGEMENT,
                FeaturePermission.CREATE_COMPETITION
            ],
            "limits": {
                "max_teachers": 10,
                "max_students": 500,
                "max_competitions_per_month": 5,
                "max_classrooms": 20
            },
            "is_default": True
        },
        PlanType.PREMIUM: {
            "id": "institute_premium",
            "name": "Institute Premium",
            "description": "Premium plan for institutes with advanced features",
            "price": 9999,  # $99.99
            "duration_days": 30,
            "features": [
                FeaturePermission.TEACHER_MANAGEMENT,
                FeaturePermission.MENTOR_MANAGEMENT,
                FeaturePermission.CREATE_COMPETITION,
                FeaturePermission.INSTITUTE_COMPETITIONS,
                FeaturePermission.AI_QUESTION_GENERATION,
                FeaturePermission.ADVANCED_ANALYTICS,
                FeaturePermission.CUSTOM_BRANDING,
                FeaturePermission.API_ACCESS,
                FeaturePermission.PRIORITY_SUPPORT
            ],
            "limits": {
                "max_teachers": 100,
                "max_students": 5000,
                "max_competitions_per_month": 50,
                "max_classrooms": 200,
                "ai_questions_per_month": 2000
            }
        }
    },
    
    # MENTOR PLANS
    UserTypeEnum.mentor: {
        PlanType.BASIC: {
            "id": "mentor_basic",
            "name": "Mentor Basic",
            "description": "Free basic plan for mentors",
            "price": 0,
            "duration_days": 365,
            "features": [],
            "limits": {
                "max_assignments_per_month": 10,
                "max_students_to_check": 100
            },
            "is_default": True
        },
        PlanType.PREMIUM: {
            "id": "mentor_premium",
            "name": "Mentor Premium",
            "description": "Premium plan for professional mentors",
            "price": 1999,  # $19.99
            "duration_days": 30,
            "features": [
                FeaturePermission.PRIORITY_ASSIGNMENTS,
                FeaturePermission.ADVANCED_ANALYTICS,
                FeaturePermission.PAYMENT_PROCESSING,
                FeaturePermission.PRIORITY_SUPPORT
            ],
            "limits": {
                "max_assignments_per_month": 50,
                "max_students_to_check": 500
            }
        }
    },
    
    # ADMIN PLANS
    UserTypeEnum.admin: {
        PlanType.PRO: {
            "id": "admin_full",
            "name": "Admin Full Access",
            "description": "Full system access for administrators",
            "price": 0,
            "duration_days": 365,
            "features": [
                FeaturePermission.CREATE_CLASSROOM,
                FeaturePermission.CREATE_EXAM,
                FeaturePermission.CREATE_COMPETITION,
                FeaturePermission.AI_QUESTION_GENERATION,
                FeaturePermission.ADVANCED_ANALYTICS,
                FeaturePermission.HOME_TUTORING,
                FeaturePermission.MENTOR_MANAGEMENT,
                FeaturePermission.TEACHER_MANAGEMENT,
                FeaturePermission.INSTITUTE_COMPETITIONS,
                FeaturePermission.PRIORITY_ASSIGNMENTS,
                FeaturePermission.PAYMENT_PROCESSING,
                FeaturePermission.BULK_OPERATIONS,
                FeaturePermission.CUSTOM_BRANDING,
                FeaturePermission.API_ACCESS,
                FeaturePermission.PRIORITY_SUPPORT
            ],
            "limits": {},  # No limits for admin
            "is_default": True
        }
    }
}


def get_plan_config(user_type: UserTypeEnum, plan_type: PlanType) -> Optional[Dict[str, Any]]:
    """Get plan configuration for user type and plan type"""
    return SUBSCRIPTION_PLANS.get(user_type, {}).get(plan_type)


def get_default_plan(user_type: UserTypeEnum) -> Optional[Dict[str, Any]]:
    """Get default plan for user type"""
    user_plans = SUBSCRIPTION_PLANS.get(user_type, {})
    for plan_type, plan_config in user_plans.items():
        if plan_config.get("is_default", False):
            return plan_config
    return None


def get_all_plans_for_user_type(user_type: UserTypeEnum) -> Dict[PlanType, Dict[str, Any]]:
    """Get all available plans for a user type"""
    return SUBSCRIPTION_PLANS.get(user_type, {})


def has_feature_permission(user_type: UserTypeEnum, plan_type: PlanType, feature: FeaturePermission) -> bool:
    """Check if a plan has a specific feature permission"""
    plan_config = get_plan_config(user_type, plan_type)
    if not plan_config:
        return False
    
    return feature in plan_config.get("features", [])


def get_plan_limit(user_type: UserTypeEnum, plan_type: PlanType, limit_name: str) -> Optional[int]:
    """Get a specific limit value for a plan"""
    plan_config = get_plan_config(user_type, plan_type)
    if not plan_config:
        return None
    
    return plan_config.get("limits", {}).get(limit_name)


def is_within_limit(user_type: UserTypeEnum, plan_type: PlanType, limit_name: str, current_usage: int) -> bool:
    """Check if current usage is within plan limits"""
    limit = get_plan_limit(user_type, plan_type, limit_name)
    if limit is None:  # No limit means unlimited
        return True
    
    return current_usage < limit


# Feature to endpoint mapping
FEATURE_ENDPOINT_MAPPING = {
    # Classroom features
    FeaturePermission.CREATE_CLASSROOM: [
        "POST:/api/classrooms",
        "POST:/api/classrooms/*/students"
    ],
    
    # Exam features
    FeaturePermission.CREATE_EXAM: [
        "POST:/api/exams",
        "PUT:/api/exams/*"
    ],
    
    # Competition features
    FeaturePermission.CREATE_COMPETITION: [
        "POST:/api/competitions/from-exam/*",
        "POST:/api/competitions/with-questions"
    ],
    
    # AI features
    FeaturePermission.AI_QUESTION_GENERATION: [
        "POST:/api/competitions/*/questions/generate-ai",
        "POST:/api/exams/*/questions/generate-ai"
    ],
    
    # Analytics features
    FeaturePermission.ADVANCED_ANALYTICS: [
        "GET:/api/competitions/*/analytics",
        "GET:/api/exams/*/analytics",
        "GET:/api/classrooms/*/analytics"
    ],
    
    # Home tutoring features
    FeaturePermission.HOME_TUTORING: [
        "POST:/api/subscriptions/teacher/enable-home-tutoring",
        "PUT:/api/subscriptions/teacher/profile"
    ],
    
    # Institute features
    FeaturePermission.MENTOR_MANAGEMENT: [
        "POST:/api/competitions/*/assign-mentor",
        "GET:/api/competitions/*/mentor-assignments"
    ],
    
    FeaturePermission.TEACHER_MANAGEMENT: [
        "POST:/api/institutes/*/teachers",
        "DELETE:/api/institutes/*/teachers/*"
    ],
    
    # Mentor features
    FeaturePermission.PRIORITY_ASSIGNMENTS: [
        "GET:/api/mentor/assignments",
        "POST:/api/mentor/assignments/*/respond"
    ],
    
    # Bulk operations
    FeaturePermission.BULK_OPERATIONS: [
        "POST:/api/competitions/*/questions/bulk-create",
        "POST:/api/mentor/answers/bulk-score"
    ]
}


def get_required_feature_for_endpoint(method: str, path: str) -> Optional[FeaturePermission]:
    """Get the required feature permission for an endpoint"""
    endpoint = f"{method}:{path}"
    
    for feature, endpoints in FEATURE_ENDPOINT_MAPPING.items():
        for pattern in endpoints:
            # Simple pattern matching (can be enhanced with regex)
            if pattern.replace("*", "").replace("/", "") in endpoint.replace("/", ""):
                return feature
    
    return None
