from .baseModel import BaseModel
from sqlalchemy import Column, String, ForeignKey, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

class Announcement(BaseModel):
    __tablename__ = "announcements"
    title = Column(String, nullable=False)
    content = Column(String, nullable=False)
    classroom_id = Column(ForeignKey("classrooms.id"), nullable=False)
    classroom = relationship("Classroom", back_populates="announcements")