from typing import List, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone
import secrets
import string

# Import Models
from Models.Events import (
    Event, EventCategory, EventLocation, EventSpeaker, EventTicket, 
    EventRegistration, EventPayment, EventAttendance, EventFeedback,
    EventStatusEnum, RegistrationStatusEnum, PaymentStatusEnum
)
from Models.users import User, UserTypeEnum
from Models.Exam import Exam

# Import Schemas
from Schemas.Events.Events import (
    EventCreate, EventUpdate, EventOut, EventDetailedOut, EventMinimalOut,
    EventCategoryCreate, EventCategoryUpdate, EventCategoryOut,
    EventLocationCreate, EventLocationUpdate, EventLocationOut,
    EventSpeakerCreate, EventSpeakerUpdate, EventSpeakerOut,
    EventTicketCreate, EventTicketUpdate, EventTicketOut,
    EventRegistrationCreate, EventRegistrationOut, EventRegistrationDetailedOut,
    EventPaymentCreate, EventPaymentOut,
    EventListFilter, EventListResponse, CalendarEventOut, CalendarResponse
)


def generate_registration_number() -> str:
    """Generate a unique registration number"""
    return ''.join(secrets.choice(string.ascii_uppercase + string.digits) for _ in range(10))


# Event Category CRUD
def create_event_category(db: Session, category: EventCategoryCreate) -> EventCategoryOut:
    """Create a new event category"""
    # Check if category name already exists
    existing = db.query(EventCategory).filter(EventCategory.name == category.name).first()
    if existing:
        raise HTTPException(status_code=400, detail="Category name already exists")
    
    db_category = EventCategory(**category.dict())
    db.add(db_category)
    db.commit()
    db.refresh(db_category)
    return EventCategoryOut.model_validate(db_category)


def get_event_categories(db: Session, skip: int = 0, limit: int = 100, active_only: bool = True) -> List[EventCategoryOut]:
    """Get all event categories"""
    query = db.query(EventCategory)
    if active_only:
        query = query.filter(EventCategory.is_active == True)
    
    categories = query.offset(skip).limit(limit).all()
    return [EventCategoryOut.model_validate(cat) for cat in categories]


def get_event_category(db: Session, category_id: uuid.UUID) -> EventCategoryOut:
    """Get event category by ID"""
    category = db.query(EventCategory).filter(EventCategory.id == category_id).first()
    if not category:
        raise HTTPException(status_code=404, detail="Event category not found")
    return EventCategoryOut.model_validate(category)


def update_event_category(db: Session, category_id: uuid.UUID, category_update: EventCategoryUpdate) -> EventCategoryOut:
    """Update event category"""
    category = db.query(EventCategory).filter(EventCategory.id == category_id).first()
    if not category:
        raise HTTPException(status_code=404, detail="Event category not found")
    
    update_data = category_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(category, field, value)
    
    db.commit()
    db.refresh(category)
    return EventCategoryOut.model_validate(category)


def delete_event_category(db: Session, category_id: uuid.UUID) -> bool:
    """Delete event category"""
    category = db.query(EventCategory).filter(EventCategory.id == category_id).first()
    if not category:
        raise HTTPException(status_code=404, detail="Event category not found")
    
    # Check if category is used by any events
    events_count = db.query(Event).filter(Event.category_id == category_id).count()
    if events_count > 0:
        raise HTTPException(status_code=400, detail="Cannot delete category that is used by events")
    
    db.delete(category)
    db.commit()
    return True


# Event Location CRUD
def create_event_location(db: Session, location: EventLocationCreate) -> EventLocationOut:
    """Create a new event location"""
    db_location = EventLocation(**location.dict())
    db.add(db_location)
    db.commit()
    db.refresh(db_location)
    return EventLocationOut.model_validate(db_location)


def get_event_locations(db: Session, skip: int = 0, limit: int = 100) -> List[EventLocationOut]:
    """Get all event locations"""
    locations = db.query(EventLocation).offset(skip).limit(limit).all()
    return [EventLocationOut.model_validate(loc) for loc in locations]


def get_event_location(db: Session, location_id: uuid.UUID) -> EventLocationOut:
    """Get event location by ID"""
    location = db.query(EventLocation).filter(EventLocation.id == location_id).first()
    if not location:
        raise HTTPException(status_code=404, detail="Event location not found")
    return EventLocationOut.model_validate(location)


def update_event_location(db: Session, location_id: uuid.UUID, location_update: EventLocationUpdate) -> EventLocationOut:
    """Update event location"""
    location = db.query(EventLocation).filter(EventLocation.id == location_id).first()
    if not location:
        raise HTTPException(status_code=404, detail="Event location not found")
    
    update_data = location_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(location, field, value)
    
    db.commit()
    db.refresh(location)
    return EventLocationOut.model_validate(location)


def delete_event_location(db: Session, location_id: uuid.UUID) -> bool:
    """Delete event location"""
    location = db.query(EventLocation).filter(EventLocation.id == location_id).first()
    if not location:
        raise HTTPException(status_code=404, detail="Event location not found")
    
    # Check if location is used by any events
    events_count = db.query(Event).filter(Event.location_id == location_id).count()
    if events_count > 0:
        raise HTTPException(status_code=400, detail="Cannot delete location that is used by events")
    
    db.delete(location)
    db.commit()
    return True


# Event Speaker CRUD
def create_event_speaker(db: Session, speaker: EventSpeakerCreate) -> EventSpeakerOut:
    """Create a new event speaker"""
    db_speaker = EventSpeaker(**speaker.dict())
    db.add(db_speaker)
    db.commit()
    db.refresh(db_speaker)
    return EventSpeakerOut.model_validate(db_speaker)


def get_event_speakers(db: Session, skip: int = 0, limit: int = 100, featured_only: bool = False) -> List[EventSpeakerOut]:
    """Get all event speakers"""
    query = db.query(EventSpeaker)
    if featured_only:
        query = query.filter(EventSpeaker.is_featured == True)
    
    speakers = query.offset(skip).limit(limit).all()
    return [EventSpeakerOut.model_validate(speaker) for speaker in speakers]


def get_event_speaker(db: Session, speaker_id: uuid.UUID) -> EventSpeakerOut:
    """Get event speaker by ID"""
    speaker = db.query(EventSpeaker).filter(EventSpeaker.id == speaker_id).first()
    if not speaker:
        raise HTTPException(status_code=404, detail="Event speaker not found")
    return EventSpeakerOut.model_validate(speaker)


def update_event_speaker(db: Session, speaker_id: uuid.UUID, speaker_update: EventSpeakerUpdate) -> EventSpeakerOut:
    """Update event speaker"""
    speaker = db.query(EventSpeaker).filter(EventSpeaker.id == speaker_id).first()
    if not speaker:
        raise HTTPException(status_code=404, detail="Event speaker not found")
    
    update_data = speaker_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(speaker, field, value)
    
    db.commit()
    db.refresh(speaker)
    return EventSpeakerOut.model_validate(speaker)


def delete_event_speaker(db: Session, speaker_id: uuid.UUID) -> bool:
    """Delete event speaker"""
    speaker = db.query(EventSpeaker).filter(EventSpeaker.id == speaker_id).first()
    if not speaker:
        raise HTTPException(status_code=404, detail="Event speaker not found")
    
    db.delete(speaker)
    db.commit()
    return True


# Event CRUD
def create_event(db: Session, event: EventCreate, organizer_id: uuid.UUID) -> EventOut:
    """Create a new event"""
    # Verify organizer exists and is a teacher
    organizer = db.query(User).filter(
        User.id == organizer_id,
        User.user_type == UserTypeEnum.teacher
    ).first()
    if not organizer:
        raise HTTPException(status_code=404, detail="Organizer not found or not a teacher")
    
    # Verify category exists
    category = db.query(EventCategory).filter(EventCategory.id == event.category_id).first()
    if not category:
        raise HTTPException(status_code=404, detail="Event category not found")
    
    # Verify location exists if provided
    if event.location_id:
        location = db.query(EventLocation).filter(EventLocation.id == event.location_id).first()
        if not location:
            raise HTTPException(status_code=404, detail="Event location not found")
    
    # Verify exam exists if this is a competition
    if event.is_competition and event.competition_exam_id:
        exam = db.query(Exam).filter(Exam.id == event.competition_exam_id).first()
        if not exam:
            raise HTTPException(status_code=404, detail="Competition exam not found")
    
    event_data = event.dict()
    event_data['organizer_id'] = organizer_id
    
    db_event = Event(**event_data)
    db.add(db_event)
    db.commit()
    db.refresh(db_event)
    return EventOut.model_validate(db_event)


def get_events(
    db: Session,
    filters: EventListFilter = None,
    skip: int = 0,
    limit: int = 20,
    current_user_id: uuid.UUID = None
) -> EventListResponse:
    """Get events with filtering and pagination"""
    query = db.query(Event).options(
        joinedload(Event.category),
        joinedload(Event.location)
    )

    # Apply filters
    if filters:
        if filters.category_id:
            query = query.filter(Event.category_id == filters.category_id)
        if filters.location_id:
            query = query.filter(Event.location_id == filters.location_id)
        if filters.status:
            query = query.filter(Event.status == filters.status)
        if filters.is_featured is not None:
            query = query.filter(Event.is_featured == filters.is_featured)
        if filters.is_public is not None:
            query = query.filter(Event.is_public == filters.is_public)
        if filters.is_competition is not None:
            query = query.filter(Event.is_competition == filters.is_competition)
        if filters.start_date:
            query = query.filter(Event.start_datetime >= filters.start_date)
        if filters.end_date:
            query = query.filter(Event.end_datetime <= filters.end_date)
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.filter(
                or_(
                    Event.title.ilike(search_term),
                    Event.description.ilike(search_term),
                    Event.short_description.ilike(search_term)
                )
            )
        if filters.tags:
            for tag in filters.tags:
                query = query.filter(Event.tags.contains([tag]))

    # Only show public events or events organized by current user
    if current_user_id:
        query = query.filter(
            or_(
                Event.is_public == True,
                Event.organizer_id == current_user_id
            )
        )
    else:
        query = query.filter(Event.is_public == True)

    # Get total count
    total = query.count()

    # Apply pagination and ordering
    events = query.order_by(desc(Event.start_datetime)).offset(skip).limit(limit).all()

    # Convert to response format
    event_list = [EventMinimalOut.model_validate(event) for event in events]

    return EventListResponse(
        events=event_list,
        total=total,
        page=(skip // limit) + 1,
        size=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0
    )


def get_event(db: Session, event_id: uuid.UUID, current_user_id: uuid.UUID = None) -> EventDetailedOut:
    """Get event by ID with detailed information"""
    event = db.query(Event).options(
        joinedload(Event.category),
        joinedload(Event.location),
        joinedload(Event.speakers),
        joinedload(Event.tickets)
    ).filter(Event.id == event_id).first()

    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    # Check if user can view this event
    if not event.is_public and (not current_user_id or event.organizer_id != current_user_id):
        raise HTTPException(status_code=403, detail="Access denied to this event")

    # Calculate additional statistics
    total_registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).count()

    available_tickets = 0
    for ticket in event.tickets:
        if ticket.total_quantity:
            available_tickets += max(0, ticket.total_quantity - ticket.sold_quantity)
        else:
            available_tickets = -1  # Unlimited
            break

    # Convert to response format
    event_data = EventDetailedOut.model_validate(event)
    event_data.total_registrations = total_registrations
    event_data.available_tickets = available_tickets

    return event_data


def update_event(db: Session, event_id: uuid.UUID, event_update: EventUpdate, organizer_id: uuid.UUID) -> EventOut:
    """Update event"""
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    # Check if user is the organizer
    if event.organizer_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only the event organizer can update this event")

    # Validate updates
    update_data = event_update.dict(exclude_unset=True)

    if 'category_id' in update_data:
        category = db.query(EventCategory).filter(EventCategory.id == update_data['category_id']).first()
        if not category:
            raise HTTPException(status_code=404, detail="Event category not found")

    if 'location_id' in update_data and update_data['location_id']:
        location = db.query(EventLocation).filter(EventLocation.id == update_data['location_id']).first()
        if not location:
            raise HTTPException(status_code=404, detail="Event location not found")

    if 'competition_exam_id' in update_data and update_data['competition_exam_id']:
        exam = db.query(Exam).filter(Exam.id == update_data['competition_exam_id']).first()
        if not exam:
            raise HTTPException(status_code=404, detail="Competition exam not found")

    # Apply updates
    for field, value in update_data.items():
        setattr(event, field, value)

    db.commit()
    db.refresh(event)
    return EventOut.model_validate(event)


def delete_event(db: Session, event_id: uuid.UUID, organizer_id: uuid.UUID) -> bool:
    """Delete event"""
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    # Check if user is the organizer
    if event.organizer_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only the event organizer can delete this event")

    # Check if event has registrations
    registrations_count = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).count()

    if registrations_count > 0:
        raise HTTPException(status_code=400, detail="Cannot delete event with confirmed registrations")

    db.delete(event)
    db.commit()
    return True


# Event Ticket CRUD
def create_event_ticket(db: Session, ticket: EventTicketCreate, organizer_id: uuid.UUID) -> EventTicketOut:
    """Create a new event ticket"""
    # Verify event exists and user is organizer
    event = db.query(Event).filter(Event.id == ticket.event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    if event.organizer_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only the event organizer can create tickets")

    db_ticket = EventTicket(**ticket.dict())
    db.add(db_ticket)
    db.commit()
    db.refresh(db_ticket)
    return EventTicketOut.model_validate(db_ticket)


def get_event_tickets(db: Session, event_id: uuid.UUID) -> List[EventTicketOut]:
    """Get all tickets for an event"""
    tickets = db.query(EventTicket).filter(EventTicket.event_id == event_id).all()
    return [EventTicketOut.model_validate(ticket) for ticket in tickets]


def update_event_ticket(db: Session, ticket_id: uuid.UUID, ticket_update: EventTicketUpdate, organizer_id: uuid.UUID) -> EventTicketOut:
    """Update event ticket"""
    ticket = db.query(EventTicket).filter(EventTicket.id == ticket_id).first()
    if not ticket:
        raise HTTPException(status_code=404, detail="Event ticket not found")

    # Verify user is event organizer
    event = db.query(Event).filter(Event.id == ticket.event_id).first()
    if not event or event.organizer_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only the event organizer can update tickets")

    update_data = ticket_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(ticket, field, value)

    db.commit()
    db.refresh(ticket)
    return EventTicketOut.model_validate(ticket)


def delete_event_ticket(db: Session, ticket_id: uuid.UUID, organizer_id: uuid.UUID) -> bool:
    """Delete event ticket"""
    ticket = db.query(EventTicket).filter(EventTicket.id == ticket_id).first()
    if not ticket:
        raise HTTPException(status_code=404, detail="Event ticket not found")

    # Verify user is event organizer
    event = db.query(Event).filter(Event.id == ticket.event_id).first()
    if not event or event.organizer_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only the event organizer can delete tickets")

    # Check if ticket has registrations
    registrations_count = db.query(EventRegistration).filter(
        EventRegistration.ticket_id == ticket_id,
        EventRegistration.status.in_([RegistrationStatusEnum.CONFIRMED, RegistrationStatusEnum.ATTENDED])
    ).count()

    if registrations_count > 0:
        raise HTTPException(status_code=400, detail="Cannot delete ticket with confirmed registrations")

    db.delete(ticket)
    db.commit()
    return True


# Event Registration CRUD
def create_event_registration(db: Session, registration: EventRegistrationCreate, user_id: uuid.UUID) -> EventRegistrationOut:
    """Create a new event registration"""
    # Verify event exists and is open for registration
    event = db.query(Event).filter(Event.id == registration.event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    if event.status != EventStatusEnum.PUBLISHED:
        raise HTTPException(status_code=400, detail="Event is not open for registration")

    # Check registration timing
    now = datetime.now(timezone.utc)
    if event.registration_start and now < event.registration_start:
        raise HTTPException(status_code=400, detail="Registration has not started yet")

    if event.registration_end and now > event.registration_end:
        raise HTTPException(status_code=400, detail="Registration has ended")

    # Verify ticket if specified
    ticket = None
    total_amount = 0
    if registration.ticket_id:
        ticket = db.query(EventTicket).filter(
            EventTicket.id == registration.ticket_id,
            EventTicket.event_id == registration.event_id
        ).first()
        if not ticket:
            raise HTTPException(status_code=404, detail="Event ticket not found")

        # Check ticket availability
        if ticket.total_quantity and (ticket.sold_quantity + registration.quantity) > ticket.total_quantity:
            raise HTTPException(status_code=400, detail="Not enough tickets available")

        # Check quantity limits
        if registration.quantity < ticket.min_quantity_per_order:
            raise HTTPException(status_code=400, detail=f"Minimum quantity is {ticket.min_quantity_per_order}")

        if ticket.max_quantity_per_order and registration.quantity > ticket.max_quantity_per_order:
            raise HTTPException(status_code=400, detail=f"Maximum quantity is {ticket.max_quantity_per_order}")

        total_amount = ticket.price * registration.quantity

    # Check if user already registered
    existing_registration = db.query(EventRegistration).filter(
        EventRegistration.event_id == registration.event_id,
        EventRegistration.user_id == user_id,
        EventRegistration.status.in_([RegistrationStatusEnum.PENDING, RegistrationStatusEnum.CONFIRMED])
    ).first()

    if existing_registration:
        raise HTTPException(status_code=400, detail="User already registered for this event")

    # Generate registration number
    registration_number = generate_registration_number()
    while db.query(EventRegistration).filter(EventRegistration.registration_number == registration_number).first():
        registration_number = generate_registration_number()

    # Create registration
    registration_data = registration.dict()
    registration_data.update({
        'user_id': user_id,
        'registration_number': registration_number,
        'total_amount': total_amount,
        'currency': ticket.currency if ticket else 'USD',
        'status': RegistrationStatusEnum.PENDING if event.requires_approval else RegistrationStatusEnum.CONFIRMED,
        'payment_status': PaymentStatusEnum.PENDING if total_amount > 0 else PaymentStatusEnum.COMPLETED
    })

    db_registration = EventRegistration(**registration_data)
    db.add(db_registration)

    # Update ticket sold quantity
    if ticket:
        ticket.sold_quantity += registration.quantity

    db.commit()
    db.refresh(db_registration)
    return EventRegistrationOut.model_validate(db_registration)


def get_user_registrations(db: Session, user_id: uuid.UUID, skip: int = 0, limit: int = 20) -> List[EventRegistrationDetailedOut]:
    """Get user's event registrations"""
    registrations = db.query(EventRegistration).options(
        joinedload(EventRegistration.event),
        joinedload(EventRegistration.ticket)
    ).filter(EventRegistration.user_id == user_id).offset(skip).limit(limit).all()

    return [EventRegistrationDetailedOut.model_validate(reg) for reg in registrations]


def get_event_registrations(db: Session, event_id: uuid.UUID, organizer_id: uuid.UUID, skip: int = 0, limit: int = 50) -> List[EventRegistrationOut]:
    """Get registrations for an event (organizer only)"""
    # Verify user is event organizer
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    if event.organizer_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only the event organizer can view registrations")

    registrations = db.query(EventRegistration).filter(
        EventRegistration.event_id == event_id
    ).offset(skip).limit(limit).all()

    return [EventRegistrationOut.model_validate(reg) for reg in registrations]


def cancel_event_registration(db: Session, registration_id: uuid.UUID, user_id: uuid.UUID) -> EventRegistrationOut:
    """Cancel event registration"""
    registration = db.query(EventRegistration).filter(
        EventRegistration.id == registration_id,
        EventRegistration.user_id == user_id
    ).first()

    if not registration:
        raise HTTPException(status_code=404, detail="Registration not found")

    if registration.status == RegistrationStatusEnum.CANCELLED:
        raise HTTPException(status_code=400, detail="Registration is already cancelled")

    if registration.status == RegistrationStatusEnum.ATTENDED:
        raise HTTPException(status_code=400, detail="Cannot cancel registration after attending event")

    # Update registration status
    registration.status = RegistrationStatusEnum.CANCELLED
    registration.cancelled_at = datetime.now(timezone.utc)

    # Update ticket sold quantity
    if registration.ticket_id:
        ticket = db.query(EventTicket).filter(EventTicket.id == registration.ticket_id).first()
        if ticket:
            ticket.sold_quantity = max(0, ticket.sold_quantity - registration.quantity)

    db.commit()
    db.refresh(registration)
    return EventRegistrationOut.model_validate(registration)


# Calendar and Utility Functions
def get_calendar_events(db: Session, year: int, month: int, user_id: uuid.UUID = None) -> CalendarResponse:
    """Get events for calendar view"""
    from calendar import monthrange

    # Get first and last day of the month
    first_day = datetime(year, month, 1, tzinfo=timezone.utc)
    last_day_num = monthrange(year, month)[1]
    last_day = datetime(year, month, last_day_num, 23, 59, 59, tzinfo=timezone.utc)

    # Query events in the month
    query = db.query(Event).options(
        joinedload(Event.category),
        joinedload(Event.location)
    ).filter(
        Event.start_datetime >= first_day,
        Event.start_datetime <= last_day,
        Event.status == EventStatusEnum.PUBLISHED,
        Event.is_public == True
    )

    events = query.all()

    # Convert to calendar format
    calendar_events = []
    for event in events:
        # Check if user is registered
        is_registered = False
        registration_status = None

        if user_id:
            registration = db.query(EventRegistration).filter(
                EventRegistration.event_id == event.id,
                EventRegistration.user_id == user_id
            ).first()

            if registration:
                is_registered = True
                registration_status = registration.status

        calendar_event = CalendarEventOut(
            id=event.id,
            title=event.title,
            start_datetime=event.start_datetime,
            end_datetime=event.end_datetime,
            category=EventCategoryOut.model_validate(event.category),
            location=EventLocationOut.model_validate(event.location) if event.location else None,
            is_registered=is_registered,
            registration_status=registration_status
        )
        calendar_events.append(calendar_event)

    return CalendarResponse(
        events=calendar_events,
        month=month,
        year=year
    )


def get_featured_events(db: Session, limit: int = 5) -> List[EventMinimalOut]:
    """Get featured events"""
    events = db.query(Event).filter(
        Event.is_featured == True,
        Event.status == EventStatusEnum.PUBLISHED,
        Event.is_public == True,
        Event.start_datetime > datetime.now(timezone.utc)
    ).order_by(Event.start_datetime).limit(limit).all()

    return [EventMinimalOut.model_validate(event) for event in events]


def get_upcoming_events(db: Session, limit: int = 10, category_id: uuid.UUID = None) -> List[EventMinimalOut]:
    """Get upcoming events"""
    query = db.query(Event).filter(
        Event.status == EventStatusEnum.PUBLISHED,
        Event.is_public == True,
        Event.start_datetime > datetime.now(timezone.utc)
    )

    if category_id:
        query = query.filter(Event.category_id == category_id)

    events = query.order_by(Event.start_datetime).limit(limit).all()
    return [EventMinimalOut.model_validate(event) for event in events]


def search_events(db: Session, search_term: str, limit: int = 20) -> List[EventMinimalOut]:
    """Search events by title, description, or tags"""
    search_pattern = f"%{search_term}%"

    events = db.query(Event).filter(
        Event.status == EventStatusEnum.PUBLISHED,
        Event.is_public == True,
        or_(
            Event.title.ilike(search_pattern),
            Event.description.ilike(search_pattern),
            Event.short_description.ilike(search_pattern)
        )
    ).order_by(Event.start_datetime).limit(limit).all()

    return [EventMinimalOut.model_validate(event) for event in events]


def add_speaker_to_event(db: Session, event_id: uuid.UUID, speaker_id: uuid.UUID, organizer_id: uuid.UUID) -> bool:
    """Add speaker to event"""
    # Verify event exists and user is organizer
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    if event.organizer_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only the event organizer can add speakers")

    # Verify speaker exists
    speaker = db.query(EventSpeaker).filter(EventSpeaker.id == speaker_id).first()
    if not speaker:
        raise HTTPException(status_code=404, detail="Speaker not found")

    # Check if speaker is already added
    if speaker in event.speakers:
        raise HTTPException(status_code=400, detail="Speaker is already added to this event")

    event.speakers.append(speaker)
    db.commit()
    return True


def remove_speaker_from_event(db: Session, event_id: uuid.UUID, speaker_id: uuid.UUID, organizer_id: uuid.UUID) -> bool:
    """Remove speaker from event"""
    # Verify event exists and user is organizer
    event = db.query(Event).filter(Event.id == event_id).first()
    if not event:
        raise HTTPException(status_code=404, detail="Event not found")

    if event.organizer_id != organizer_id:
        raise HTTPException(status_code=403, detail="Only the event organizer can remove speakers")

    # Verify speaker exists and is associated with event
    speaker = db.query(EventSpeaker).filter(EventSpeaker.id == speaker_id).first()
    if not speaker:
        raise HTTPException(status_code=404, detail="Speaker not found")

    if speaker not in event.speakers:
        raise HTTPException(status_code=400, detail="Speaker is not associated with this event")

    event.speakers.remove(speaker)
    db.commit()
    return True
